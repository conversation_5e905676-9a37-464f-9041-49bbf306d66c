package com.example.tcs.utils

import android.content.Context
import android.util.Log
import androidx.camera.core.ImageCaptureException
import androidx.compose.material3.SnackbarHostState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * 错误处理工具类
 */
object ErrorHandler {
    
    private const val TAG = "ErrorHandler"
    
    /**
     * 处理相机错误
     */
    fun handleCameraError(
        error: Throwable,
        context: Context,
        snackbarHostState: SnackbarHostState? = null,
        scope: CoroutineScope? = null
    ) {
        val message = when (error) {
            is ImageCaptureException -> {
                when (error.imageCaptureError) {
                    ImageCaptureException.ERROR_UNKNOWN -> "拍照时发生未知错误"
                    ImageCaptureException.ERROR_FILE_IO -> "文件保存失败"
                    ImageCaptureException.ERROR_CAPTURE_FAILED -> "拍照失败"
                    ImageCaptureException.ERROR_CAMERA_CLOSED -> "相机已关闭"
                    ImageCaptureException.ERROR_INVALID_CAMERA -> "无效的相机"
                    else -> "拍照错误: ${error.message}"
                }
            }
            is SecurityException -> "缺少必要权限"
            is IllegalStateException -> "相机状态异常"
            is IllegalArgumentException -> "参数错误"
            else -> "相机错误: ${error.message ?: "未知错误"}"
        }
        
        Log.e(TAG, "Camera error: $message", error)
        
        // 显示错误消息
        if (snackbarHostState != null && scope != null) {
            scope.launch {
                snackbarHostState.showSnackbar(message)
            }
        }
    }
    
    /**
     * 处理权限错误
     */
    fun handlePermissionError(
        missingPermissions: List<String>,
        context: Context,
        snackbarHostState: SnackbarHostState? = null,
        scope: CoroutineScope? = null
    ) {
        val message = when {
            missingPermissions.contains(android.Manifest.permission.CAMERA) -> 
                "需要相机权限才能使用拍照功能"
            missingPermissions.contains(android.Manifest.permission.RECORD_AUDIO) -> 
                "需要录音权限才能录制视频"
            missingPermissions.any { it.contains("STORAGE") || it.contains("MEDIA") } -> 
                "需要存储权限才能保存照片和视频"
            else -> "缺少必要权限: ${missingPermissions.joinToString(", ")}"
        }
        
        Log.w(TAG, "Permission error: $message")
        
        if (snackbarHostState != null && scope != null) {
            scope.launch {
                snackbarHostState.showSnackbar(message)
            }
        }
    }
    
    /**
     * 处理文件操作错误
     */
    fun handleFileError(
        error: Throwable,
        operation: String,
        context: Context,
        snackbarHostState: SnackbarHostState? = null,
        scope: CoroutineScope? = null
    ) {
        val message = when (error) {
            is java.io.FileNotFoundException -> "文件不存在"
            is java.io.IOException -> "文件操作失败"
            is SecurityException -> "没有文件访问权限"
            else -> "$operation 失败: ${error.message ?: "未知错误"}"
        }
        
        Log.e(TAG, "File error during $operation: $message", error)
        
        if (snackbarHostState != null && scope != null) {
            scope.launch {
                snackbarHostState.showSnackbar(message)
            }
        }
    }
    
    /**
     * 处理网络错误
     */
    fun handleNetworkError(
        error: Throwable,
        context: Context,
        snackbarHostState: SnackbarHostState? = null,
        scope: CoroutineScope? = null
    ) {
        val message = when (error) {
            is java.net.UnknownHostException -> "网络连接失败"
            is java.net.SocketTimeoutException -> "网络请求超时"
            is java.net.ConnectException -> "无法连接到服务器"
            else -> "网络错误: ${error.message ?: "未知错误"}"
        }
        
        Log.e(TAG, "Network error: $message", error)
        
        if (snackbarHostState != null && scope != null) {
            scope.launch {
                snackbarHostState.showSnackbar(message)
            }
        }
    }
    
    /**
     * 处理一般错误
     */
    fun handleGeneralError(
        error: Throwable,
        context: Context,
        customMessage: String? = null,
        snackbarHostState: SnackbarHostState? = null,
        scope: CoroutineScope? = null
    ) {
        val message = customMessage ?: "操作失败: ${error.message ?: "未知错误"}"
        
        Log.e(TAG, "General error: $message", error)
        
        if (snackbarHostState != null && scope != null) {
            scope.launch {
                snackbarHostState.showSnackbar(message)
            }
        }
    }
    
    /**
     * 记录信息日志
     */
    fun logInfo(tag: String, message: String) {
        Log.i(tag, message)
    }
    
    /**
     * 记录调试日志
     */
    fun logDebug(tag: String, message: String) {
        Log.d(tag, message)
    }
    
    /**
     * 记录警告日志
     */
    fun logWarning(tag: String, message: String) {
        Log.w(tag, message)
    }
    
    /**
     * 记录错误日志
     */
    fun logError(tag: String, message: String, throwable: Throwable? = null) {
        if (throwable != null) {
            Log.e(tag, message, throwable)
        } else {
            Log.e(tag, message)
        }
    }
}
