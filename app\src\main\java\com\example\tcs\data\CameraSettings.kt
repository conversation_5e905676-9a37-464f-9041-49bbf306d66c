package com.example.tcs.data

import androidx.camera.core.AspectRatio
import androidx.camera.core.ImageCapture

/**
 * 相机设置数据类
 */
data class CameraSettings(
    // 基础设置
    val flashMode: Int = ImageCapture.FLASH_MODE_AUTO,
    val aspectRatio: Int = AspectRatio.RATIO_4_3,
    val captureMode: CaptureMode = CaptureMode.PHOTO,
    val lensFacing: Int = androidx.camera.core.CameraSelector.LENS_FACING_BACK,
    
    // 图像质量设置
    val imageQuality: ImageQuality = ImageQuality.HIGH,
    val videoQuality: VideoQuality = VideoQuality.HD,
    
    // 高级设置
    val gridLines: Boolean = false,
    val timerDelay: TimerDelay = TimerDelay.OFF,
    val hdr: Boolean = false,
    val stabilization: Boolean = true,
    
    // 滤镜设置
    val filter: CameraFilter = CameraFilter.NONE,
    
    // 专业模式设置
    val manualMode: Boolean = false,
    val iso: Int = 100,
    val exposureCompensation: Int = 0,
    val whiteBalance: WhiteBalance = WhiteBalance.AUTO,
    
    // 其他设置
    val soundEnabled: Boolean = true,
    val locationTagging: Boolean = false,
    val mirrorFrontCamera: Boolean = true
)

/**
 * 拍摄模式
 */
enum class CaptureMode {
    PHOTO,      // 拍照
    VIDEO,      // 录像
    PORTRAIT,   // 人像
    NIGHT,      // 夜景
    PANORAMA,   // 全景
    TIME_LAPSE, // 延时摄影
    SLOW_MOTION // 慢动作
}

/**
 * 图像质量
 */
enum class ImageQuality(val displayName: String, val megaPixels: String) {
    LOW("低质量", "2MP"),
    MEDIUM("中等质量", "8MP"),
    HIGH("高质量", "12MP"),
    ULTRA("超高质量", "48MP")
}

/**
 * 视频质量
 */
enum class VideoQuality(val displayName: String, val resolution: String) {
    SD("标清", "480p"),
    HD("高清", "720p"),
    FULL_HD("全高清", "1080p"),
    UHD_4K("4K超高清", "2160p")
}

/**
 * 定时器延迟
 */
enum class TimerDelay(val displayName: String, val seconds: Int) {
    OFF("关闭", 0),
    THREE_SEC("3秒", 3),
    FIVE_SEC("5秒", 5),
    TEN_SEC("10秒", 10)
}

/**
 * 相机滤镜
 */
enum class CameraFilter(val displayName: String) {
    NONE("无滤镜"),
    VINTAGE("复古"),
    BLACK_WHITE("黑白"),
    SEPIA("棕褐色"),
    VIVID("鲜艳"),
    WARM("暖色调"),
    COOL("冷色调"),
    DRAMATIC("戏剧性"),
    SOFT("柔和")
}

/**
 * 白平衡
 */
enum class WhiteBalance(val displayName: String) {
    AUTO("自动"),
    DAYLIGHT("日光"),
    CLOUDY("阴天"),
    TUNGSTEN("钨丝灯"),
    FLUORESCENT("荧光灯"),
    SHADE("阴影")
}

/**
 * 闪光灯模式扩展
 */
object FlashMode {
    const val AUTO = ImageCapture.FLASH_MODE_AUTO
    const val ON = ImageCapture.FLASH_MODE_ON
    const val OFF = ImageCapture.FLASH_MODE_OFF
    
    fun getDisplayName(mode: Int): String = when (mode) {
        AUTO -> "自动"
        ON -> "开启"
        OFF -> "关闭"
        else -> "未知"
    }
}

/**
 * 宽高比扩展
 */
object AspectRatioHelper {
    fun getDisplayName(ratio: Int): String = when (ratio) {
        AspectRatio.RATIO_4_3 -> "4:3"
        AspectRatio.RATIO_16_9 -> "16:9"
        else -> "1:1"
    }
    
    fun getAllRatios(): List<Pair<Int, String>> = listOf(
        AspectRatio.RATIO_4_3 to "4:3",
        AspectRatio.RATIO_16_9 to "16:9"
    )
}
