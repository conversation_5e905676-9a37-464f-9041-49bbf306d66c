package com.example.tcs.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.content.FileProvider
import java.io.File

/**
 * 分享工具类
 */
object ShareUtils {
    
    /**
     * 分享图片
     */
    fun shareImage(context: Context, imageUri: Uri) {
        try {
            val shareIntent = Intent().apply {
                action = Intent.ACTION_SEND
                type = "image/*"
                putExtra(Intent.EXTRA_STREAM, imageUri)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }
            
            val chooser = Intent.createChooser(shareIntent, "分享图片")
            context.startActivity(chooser)
        } catch (e: Exception) {
            // 处理分享失败
        }
    }
    
    /**
     * 分享视频
     */
    fun shareVideo(context: Context, videoUri: Uri) {
        try {
            val shareIntent = Intent().apply {
                action = Intent.ACTION_SEND
                type = "video/*"
                putExtra(Intent.EXTRA_STREAM, videoUri)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }
            
            val chooser = Intent.createChooser(shareIntent, "分享视频")
            context.startActivity(chooser)
        } catch (e: Exception) {
            // 处理分享失败
        }
    }
    
    /**
     * 分享文件
     */
    fun shareFile(context: Context, file: File) {
        try {
            val uri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.fileprovider",
                file
            )
            
            val mimeType = MediaUtils.getMimeType(file.name)
            
            val shareIntent = Intent().apply {
                action = Intent.ACTION_SEND
                type = mimeType
                putExtra(Intent.EXTRA_STREAM, uri)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }
            
            val chooser = Intent.createChooser(shareIntent, "分享文件")
            context.startActivity(chooser)
        } catch (e: Exception) {
            // 处理分享失败
        }
    }
    
    /**
     * 分享多个文件
     */
    fun shareMultipleFiles(context: Context, files: List<File>) {
        try {
            val uris = files.map { file ->
                FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.fileprovider",
                    file
                )
            }
            
            val shareIntent = Intent().apply {
                action = Intent.ACTION_SEND_MULTIPLE
                type = "*/*"
                putParcelableArrayListExtra(Intent.EXTRA_STREAM, ArrayList(uris))
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }
            
            val chooser = Intent.createChooser(shareIntent, "分享文件")
            context.startActivity(chooser)
        } catch (e: Exception) {
            // 处理分享失败
        }
    }
    
    /**
     * 分享文本
     */
    fun shareText(context: Context, text: String, subject: String? = null) {
        try {
            val shareIntent = Intent().apply {
                action = Intent.ACTION_SEND
                type = "text/plain"
                putExtra(Intent.EXTRA_TEXT, text)
                subject?.let { putExtra(Intent.EXTRA_SUBJECT, it) }
            }
            
            val chooser = Intent.createChooser(shareIntent, "分享文本")
            context.startActivity(chooser)
        } catch (e: Exception) {
            // 处理分享失败
        }
    }
}
