  Manifest android  CAMERA android.Manifest.permission  READ_EXTERNAL_STORAGE android.Manifest.permission  READ_MEDIA_IMAGES android.Manifest.permission  READ_MEDIA_VIDEO android.Manifest.permission  RECORD_AUDIO android.Manifest.permission  WRITE_EXTERNAL_STORAGE android.Manifest.permission  Activity android.app  Application android.app  	CameraApp android.app.Activity  TcsTheme android.app.Activity  enableEdgeToEdge android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  
ContentValues android.content  Context android.content  Intent android.content  delete android.content.ContentResolver  insert android.content.ContentResolver  openInputStream android.content.ContentResolver  openOutputStream android.content.ContentResolver  query android.content.ContentResolver  Environment android.content.ContentValues  IMAGE_MIME_TYPE android.content.ContentValues  
MediaStore android.content.ContentValues  apply android.content.ContentValues  put android.content.ContentValues  	CameraApp android.content.Context  TcsTheme android.content.Context  contentResolver android.content.Context  enableEdgeToEdge android.content.Context  getExternalFilesDir android.content.Context  packageName android.content.Context  
setContent android.content.Context  
startActivity android.content.Context  	CameraApp android.content.ContextWrapper  TcsTheme android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  
setContent android.content.ContextWrapper  ACTION_SEND android.content.Intent  ACTION_SEND_MULTIPLE android.content.Intent  	ArrayList android.content.Intent  EXTRA_STREAM android.content.Intent  
EXTRA_SUBJECT android.content.Intent  
EXTRA_TEXT android.content.Intent  FLAG_GRANT_READ_URI_PERMISSION android.content.Intent  Intent android.content.Intent  action android.content.Intent  addFlags android.content.Intent  apply android.content.Intent  
createChooser android.content.Intent  let android.content.Intent  putExtra android.content.Intent  putParcelableArrayListExtra android.content.Intent  type android.content.Intent  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  Cursor android.database  getColumnIndexOrThrow android.database.Cursor  getLong android.database.Cursor  	getString android.database.Cursor  
moveToNext android.database.Cursor  use android.database.Cursor  	Alignment android.graphics  Arrangement android.graphics  	ArrowBack android.graphics  
AsyncImage android.graphics  Bitmap android.graphics  
BitmapFactory android.graphics  Boolean android.graphics  Box android.graphics  Brightness6 android.graphics  BrightnessControl android.graphics  Canvas android.graphics  Card android.graphics  CardDefaults android.graphics  CircleShape android.graphics  ClosedFloatingPointRange android.graphics  Color android.graphics  ColorMatrix android.graphics  ColorMatrixColorFilter android.graphics  Colorize android.graphics  Column android.graphics  
Composable android.graphics  ContentScale android.graphics  Context android.graphics  Contrast android.graphics  ContrastControl android.graphics  Crop android.graphics  Dispatchers android.graphics  EditTool android.graphics  EditToolbar android.graphics  	Exception android.graphics  ExperimentalMaterial3Api android.graphics  
FilterChip android.graphics  
FilterControl android.graphics  
FilterVintage android.graphics  Flip android.graphics  FlipCameraAndroid android.graphics  Float android.graphics  
FontWeight android.graphics  Icon android.graphics  
IconButton android.graphics  Icons android.graphics  IllegalArgumentException android.graphics  ImageEditState android.graphics  ImageEditorViewModel android.graphics  ImageFilter android.graphics  InputStream android.graphics  LaunchedEffect android.graphics  LazyRow android.graphics  
MaterialTheme android.graphics  Matrix android.graphics  
MediaUtils android.graphics  Modifier android.graphics  MutableStateFlow android.graphics  OptIn android.graphics  Paint android.graphics  
RotateControl android.graphics  
RotateLeft android.graphics  RotateRight android.graphics  RoundedCornerShape android.graphics  Row android.graphics  SaturationControl android.graphics  Scaffold android.graphics  Slider android.graphics  Spacer android.graphics  	StateFlow android.graphics  String android.graphics  Text android.graphics  
TextButton android.graphics  
ToolButton android.graphics  	TopAppBar android.graphics  Unit android.graphics  Uri android.graphics  	ViewModel android.graphics  
_editState android.graphics  androidx android.graphics  applyColorAdjustments android.graphics  applyImageFilter android.graphics  
asImageBitmap android.graphics  asStateFlow android.graphics  
background android.graphics  
cardElevation android.graphics  collectAsState android.graphics  fillMaxSize android.graphics  fillMaxWidth android.graphics  
flipBitmap android.graphics  floatArrayOf android.graphics  getValue android.graphics  height android.graphics  launch android.graphics  let android.graphics  loadBitmapFromUri android.graphics  originalBitmap android.graphics  padding android.graphics  provideDelegate android.graphics  rangeTo android.graphics  rotateBitmap android.graphics  run android.graphics  saveImageToGallery android.graphics  size android.graphics  spacedBy android.graphics  width android.graphics  withContext android.graphics  CompressFormat android.graphics.Bitmap  Config android.graphics.Bitmap  
asImageBitmap android.graphics.Bitmap  compress android.graphics.Bitmap  config android.graphics.Bitmap  copy android.graphics.Bitmap  createBitmap android.graphics.Bitmap  height android.graphics.Bitmap  let android.graphics.Bitmap  width android.graphics.Bitmap  JPEG &android.graphics.Bitmap.CompressFormat  	ARGB_8888 android.graphics.Bitmap.Config  decodeStream android.graphics.BitmapFactory  
drawBitmap android.graphics.Canvas  
postConcat android.graphics.ColorMatrix  set android.graphics.ColorMatrix  
setSaturation android.graphics.ColorMatrix  
postRotate android.graphics.Matrix  preScale android.graphics.Matrix  colorFilter android.graphics.Paint  compose android.graphics.androidx  ui !android.graphics.androidx.compose  graphics $android.graphics.androidx.compose.ui  vector -android.graphics.androidx.compose.ui.graphics  ImageVector 4android.graphics.androidx.compose.ui.graphics.vector  Uri android.net  fromFile android.net.Uri  let android.net.Uri  path android.net.Uri  scheme android.net.Uri  withAppendedPath android.net.Uri  Build 
android.os  Bundle 
android.os  Environment 
android.os  SDK_INT android.os.Build.VERSION  P android.os.Build.VERSION_CODES  Q android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  DIRECTORY_MOVIES android.os.Environment  DIRECTORY_PICTURES android.os.Environment  !getExternalStoragePublicDirectory android.os.Environment  
MediaStore android.provider  _ID android.provider.BaseColumns  
DATE_ADDED (android.provider.MediaStore.Images.Media  DISPLAY_NAME (android.provider.MediaStore.Images.Media  EXTERNAL_CONTENT_URI (android.provider.MediaStore.Images.Media  SIZE (android.provider.MediaStore.Images.Media  _ID (android.provider.MediaStore.Images.Media  DATA (android.provider.MediaStore.MediaColumns  
DATE_ADDED (android.provider.MediaStore.MediaColumns  DISPLAY_NAME (android.provider.MediaStore.MediaColumns  DURATION (android.provider.MediaStore.MediaColumns  	MIME_TYPE (android.provider.MediaStore.MediaColumns  
RELATIVE_PATH (android.provider.MediaStore.MediaColumns  SIZE (android.provider.MediaStore.MediaColumns  
DATE_ADDED 'android.provider.MediaStore.Video.Media  DISPLAY_NAME 'android.provider.MediaStore.Video.Media  DURATION 'android.provider.MediaStore.Video.Media  EXTERNAL_CONTENT_URI 'android.provider.MediaStore.Video.Media  SIZE 'android.provider.MediaStore.Video.Media  _ID 'android.provider.MediaStore.Video.Media  Log android.util  d android.util.Log  e android.util.Log  i android.util.Log  w android.util.Log  	CameraApp  android.view.ContextThemeWrapper  TcsTheme  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  	CameraApp #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  TcsTheme #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  	CameraApp -androidx.activity.ComponentActivity.Companion  TcsTheme -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  AspectRatio androidx.camera.core  Boolean androidx.camera.core  Camera androidx.camera.core  CameraSelector androidx.camera.core  CameraSettings androidx.camera.core  CameraState androidx.camera.core  ConcurrentCamera androidx.camera.core  Context androidx.camera.core  
ContextCompat androidx.camera.core  	Exception androidx.camera.core  ExecutorService androidx.camera.core  	Executors androidx.camera.core  FILENAME_FORMAT androidx.camera.core  File androidx.camera.core  FileOutputOptions androidx.camera.core  ImageCapture androidx.camera.core  ImageCaptureException androidx.camera.core  LifecycleOwner androidx.camera.core  Locale androidx.camera.core  Log androidx.camera.core  MutableStateFlow androidx.camera.core  Preview androidx.camera.core  PreviewView androidx.camera.core  ProcessCameraProvider androidx.camera.core  Quality androidx.camera.core  QualitySelector androidx.camera.core  Recorder androidx.camera.core  	Recording androidx.camera.core  SimpleDateFormat androidx.camera.core  	StateFlow androidx.camera.core  String androidx.camera.core  System androidx.camera.core  TAG androidx.camera.core  Unit androidx.camera.core  Uri androidx.camera.core  VideoCapture androidx.camera.core  VideoRecordEvent androidx.camera.core  _cameraState androidx.camera.core  also androidx.camera.core  asStateFlow androidx.camera.core  run androidx.camera.core  
RATIO_16_9  androidx.camera.core.AspectRatio  	RATIO_4_3  androidx.camera.core.AspectRatio  Builder #androidx.camera.core.CameraSelector  LENS_FACING_BACK #androidx.camera.core.CameraSelector  LENS_FACING_FRONT #androidx.camera.core.CameraSelector  build +androidx.camera.core.CameraSelector.Builder  requireLensFacing +androidx.camera.core.CameraSelector.Builder  Builder !androidx.camera.core.ImageCapture  FLASH_MODE_AUTO !androidx.camera.core.ImageCapture  FLASH_MODE_OFF !androidx.camera.core.ImageCapture  
FLASH_MODE_ON !androidx.camera.core.ImageCapture  OnImageSavedCallback !androidx.camera.core.ImageCapture  OutputFileOptions !androidx.camera.core.ImageCapture  OutputFileResults !androidx.camera.core.ImageCapture  takePicture !androidx.camera.core.ImageCapture  build )androidx.camera.core.ImageCapture.Builder  setFlashMode )androidx.camera.core.ImageCapture.Builder  setTargetAspectRatio )androidx.camera.core.ImageCapture.Builder  Builder 3androidx.camera.core.ImageCapture.OutputFileOptions  build ;androidx.camera.core.ImageCapture.OutputFileOptions.Builder  message *androidx.camera.core.ImageCaptureException  Builder androidx.camera.core.Preview  SurfaceProvider androidx.camera.core.Preview  also androidx.camera.core.Preview  setSurfaceProvider androidx.camera.core.Preview  build $androidx.camera.core.Preview.Builder  setTargetAspectRatio $androidx.camera.core.Preview.Builder  Finalize %androidx.camera.core.VideoRecordEvent  Start %androidx.camera.core.VideoRecordEvent  ProcessCameraProvider androidx.camera.lifecycle  bindToLifecycle /androidx.camera.lifecycle.ProcessCameraProvider  getInstance /androidx.camera.lifecycle.ProcessCameraProvider  	unbindAll /androidx.camera.lifecycle.ProcessCameraProvider  Boolean androidx.camera.video  Camera androidx.camera.video  CameraSelector androidx.camera.video  CameraSettings androidx.camera.video  CameraState androidx.camera.video  Context androidx.camera.video  
ContextCompat androidx.camera.video  	Exception androidx.camera.video  ExecutorService androidx.camera.video  	Executors androidx.camera.video  FILENAME_FORMAT androidx.camera.video  File androidx.camera.video  FileOutputOptions androidx.camera.video  ImageCapture androidx.camera.video  ImageCaptureException androidx.camera.video  LifecycleOwner androidx.camera.video  Locale androidx.camera.video  Log androidx.camera.video  MutableStateFlow androidx.camera.video  PendingRecording androidx.camera.video  Preview androidx.camera.video  PreviewView androidx.camera.video  ProcessCameraProvider androidx.camera.video  Quality androidx.camera.video  QualitySelector androidx.camera.video  Recorder androidx.camera.video  	Recording androidx.camera.video  SimpleDateFormat androidx.camera.video  	StateFlow androidx.camera.video  String androidx.camera.video  System androidx.camera.video  TAG androidx.camera.video  Unit androidx.camera.video  Uri androidx.camera.video  VideoCapture androidx.camera.video  VideoRecordEvent androidx.camera.video  _cameraState androidx.camera.video  also androidx.camera.video  asStateFlow androidx.camera.video  run androidx.camera.video  Builder 'androidx.camera.video.FileOutputOptions  build /androidx.camera.video.FileOutputOptions.Builder  OnImageSavedCallback "androidx.camera.video.ImageCapture  OutputFileResults "androidx.camera.video.ImageCapture  start &androidx.camera.video.PendingRecording  HIGHEST androidx.camera.video.Quality  from %androidx.camera.video.QualitySelector  Builder androidx.camera.video.Recorder  prepareRecording androidx.camera.video.Recorder  build &androidx.camera.video.Recorder.Builder  setQualitySelector &androidx.camera.video.Recorder.Builder  stop androidx.camera.video.Recording  output "androidx.camera.video.VideoCapture  
withOutput "androidx.camera.video.VideoCapture  Finalize &androidx.camera.video.VideoRecordEvent  Start &androidx.camera.video.VideoRecordEvent  error /androidx.camera.video.VideoRecordEvent.Finalize  hasError /androidx.camera.video.VideoRecordEvent.Finalize  PreviewView androidx.camera.view  PreviewView  androidx.camera.view.PreviewView  	ScaleType  androidx.camera.view.PreviewView  apply  androidx.camera.view.PreviewView  let  androidx.camera.view.PreviewView  	scaleType  androidx.camera.view.PreviewView  surfaceProvider  androidx.camera.view.PreviewView  FILL_CENTER *androidx.camera.view.PreviewView.ScaleType  AnimatedContentScope androidx.compose.animation  CameraPreviewScreen /androidx.compose.animation.AnimatedContentScope  CameraSettingsScreen /androidx.compose.animation.AnimatedContentScope  
GalleryScreen /androidx.compose.animation.AnimatedContentScope  ImageEditorScreen /androidx.compose.animation.AnimatedContentScope  ImageViewerScreen /androidx.compose.animation.AnimatedContentScope  let /androidx.compose.animation.AnimatedContentScope  Canvas androidx.compose.foundation  Image androidx.compose.foundation  
background androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  TransformableState $androidx.compose.foundation.gestures  rememberTransformableState $androidx.compose.foundation.gestures  
transformable $androidx.compose.foundation.gestures  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  AndroidView "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  	ArrowBack "androidx.compose.foundation.layout  
ArrowDropDown "androidx.compose.foundation.layout  AspectRatio "androidx.compose.foundation.layout  AspectRatioHelper "androidx.compose.foundation.layout  
AsyncImage "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  BottomControlBar "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Brightness6 "androidx.compose.foundation.layout  BrightnessControl "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Camera "androidx.compose.foundation.layout  	CameraAlt "androidx.compose.foundation.layout  
CameraEnhance "androidx.compose.foundation.layout  CameraFilter "androidx.compose.foundation.layout  CameraState "androidx.compose.foundation.layout  CameraViewModel "androidx.compose.foundation.layout  Canvas "androidx.compose.foundation.layout  
CaptureButton "androidx.compose.foundation.layout  CaptureMode "androidx.compose.foundation.layout  CaptureModeSelector "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CenterFocusStrong "androidx.compose.foundation.layout  CenterFocusWeak "androidx.compose.foundation.layout  Check "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Close "androidx.compose.foundation.layout  ClosedFloatingPointRange "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Colorize "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ContentScale "androidx.compose.foundation.layout  Context "androidx.compose.foundation.layout  Contrast "androidx.compose.foundation.layout  ContrastControl "androidx.compose.foundation.layout  Crop "androidx.compose.foundation.layout  Delete "androidx.compose.foundation.layout  DropdownMenu "androidx.compose.foundation.layout  DropdownMenuItem "androidx.compose.foundation.layout  Edit "androidx.compose.foundation.layout  EditTool "androidx.compose.foundation.layout  EditToolbar "androidx.compose.foundation.layout  EmptyGalleryMessage "androidx.compose.foundation.layout  	Exception "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  ExperimentalPermissionsApi "androidx.compose.foundation.layout  Exposure "androidx.compose.foundation.layout  
FilterChip "androidx.compose.foundation.layout  FilterChipDefaults "androidx.compose.foundation.layout  
FilterControl "androidx.compose.foundation.layout  
FilterVintage "androidx.compose.foundation.layout  	FlashAuto "androidx.compose.foundation.layout  	FlashMode "androidx.compose.foundation.layout  FlashOff "androidx.compose.foundation.layout  FlashOn "androidx.compose.foundation.layout  FlashOption "androidx.compose.foundation.layout  FlashOptionsMenu "androidx.compose.foundation.layout  Flip "androidx.compose.foundation.layout  FlipCameraAndroid "androidx.compose.foundation.layout  Float "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  GalleryViewModel "androidx.compose.foundation.layout  Grid3x3 "androidx.compose.foundation.layout  	GridCells "androidx.compose.foundation.layout  HighQuality "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  ImageEditorViewModel "androidx.compose.foundation.layout  ImageFilter "androidx.compose.foundation.layout  ImageQuality "androidx.compose.foundation.layout  ImageVector "androidx.compose.foundation.layout  Info "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  Iso "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  LazyRow "androidx.compose.foundation.layout  LazyVerticalGrid "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  LocalContentColor "androidx.compose.foundation.layout  
LocationOn "androidx.compose.foundation.layout  Long "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  	MediaFile "androidx.compose.foundation.layout  MediaThumbnail "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  Offset "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  PermissionUtils "androidx.compose.foundation.layout  PhotoLibrary "androidx.compose.foundation.layout  	PlayArrow "androidx.compose.foundation.layout  PreviewView "androidx.compose.foundation.layout  ProfessionalControl "androidx.compose.foundation.layout  RecordingStatus "androidx.compose.foundation.layout  Refresh "androidx.compose.foundation.layout  
RotateControl "androidx.compose.foundation.layout  
RotateLeft "androidx.compose.foundation.layout  RotateRight "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  SaturationControl "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  SettingItem "androidx.compose.foundation.layout  Settings "androidx.compose.foundation.layout  SettingsDropdown "androidx.compose.foundation.layout  
SettingsPanel "androidx.compose.foundation.layout  SettingsSection "androidx.compose.foundation.layout  SettingsSlider "androidx.compose.foundation.layout  SettingsSwitch "androidx.compose.foundation.layout  Share "androidx.compose.foundation.layout  
ShareUtils "androidx.compose.foundation.layout  Slider "androidx.compose.foundation.layout  SliderDefaults "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  
StatusItem "androidx.compose.foundation.layout  Stop "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  Switch "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  TimeLapseSettings "androidx.compose.foundation.layout  Timer "androidx.compose.foundation.layout  
TimerDelay "androidx.compose.foundation.layout  
ToolButton "androidx.compose.foundation.layout  	TopAppBar "androidx.compose.foundation.layout  
TopControlBar "androidx.compose.foundation.layout  Tune "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  Uri "androidx.compose.foundation.layout  VideoQuality "androidx.compose.foundation.layout  
VideoSettings "androidx.compose.foundation.layout  VolumeUp "androidx.compose.foundation.layout  WbSunny "androidx.compose.foundation.layout  WhiteBalance "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  apply "androidx.compose.foundation.layout  
asImageBitmap "androidx.compose.foundation.layout  aspectRatio "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  
cardElevation "androidx.compose.foundation.layout  	clickable "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  colors "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  delay "androidx.compose.foundation.layout  deleteImage "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  filterChipColors "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  forEachIndexed "androidx.compose.foundation.layout  format "androidx.compose.foundation.layout  formatDuration "androidx.compose.foundation.layout  
formatTime "androidx.compose.foundation.layout  getAllRatios "androidx.compose.foundation.layout  getCameraPermissions "androidx.compose.foundation.layout  getDisplayName "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  
graphicsLayer "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  indexOf "androidx.compose.foundation.layout  indexOfFirst "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  map "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  
plusAssign "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  rangeTo "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  run "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  
shareImage "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  timesAssign "androidx.compose.foundation.layout  to "androidx.compose.foundation.layout  toList "androidx.compose.foundation.layout  
transformable "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  AlertDialog +androidx.compose.foundation.layout.BoxScope  	Alignment +androidx.compose.foundation.layout.BoxScope  AndroidView +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  
ArrowDropDown +androidx.compose.foundation.layout.BoxScope  
AsyncImage +androidx.compose.foundation.layout.BoxScope  BottomControlBar +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  CameraState +androidx.compose.foundation.layout.BoxScope  Canvas +androidx.compose.foundation.layout.BoxScope  CaptureMode +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  Check +androidx.compose.foundation.layout.BoxScope  CircleShape +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  ContentScale +androidx.compose.foundation.layout.BoxScope  DropdownMenu +androidx.compose.foundation.layout.BoxScope  DropdownMenuItem +androidx.compose.foundation.layout.BoxScope  EmptyGalleryMessage +androidx.compose.foundation.layout.BoxScope  FlashOptionsMenu +androidx.compose.foundation.layout.BoxScope  	GridCells +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  LaunchedEffect +androidx.compose.foundation.layout.BoxScope  LazyVerticalGrid +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  MediaThumbnail +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Offset +androidx.compose.foundation.layout.BoxScope  
PaddingValues +androidx.compose.foundation.layout.BoxScope  	PlayArrow +androidx.compose.foundation.layout.BoxScope  PreviewView +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  
ShareUtils +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  
TextButton +androidx.compose.foundation.layout.BoxScope  
TopControlBar +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  androidx +androidx.compose.foundation.layout.BoxScope  apply +androidx.compose.foundation.layout.BoxScope  
asImageBitmap +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  
cardColors +androidx.compose.foundation.layout.BoxScope  	clickable +androidx.compose.foundation.layout.BoxScope  deleteImage +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  forEachIndexed +androidx.compose.foundation.layout.BoxScope  formatDuration +androidx.compose.foundation.layout.BoxScope  getValue +androidx.compose.foundation.layout.BoxScope  
graphicsLayer +androidx.compose.foundation.layout.BoxScope  items +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  mutableStateOf +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  provideDelegate +androidx.compose.foundation.layout.BoxScope  remember +androidx.compose.foundation.layout.BoxScope  run +androidx.compose.foundation.layout.BoxScope  setValue +androidx.compose.foundation.layout.BoxScope  
shareImage +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  
transformable +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  	ArrowBack .androidx.compose.foundation.layout.ColumnScope  AspectRatio .androidx.compose.foundation.layout.ColumnScope  AspectRatioHelper .androidx.compose.foundation.layout.ColumnScope  
AsyncImage .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  BrightnessControl .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Camera .androidx.compose.foundation.layout.ColumnScope  	CameraAlt .androidx.compose.foundation.layout.ColumnScope  
CameraEnhance .androidx.compose.foundation.layout.ColumnScope  CameraFilter .androidx.compose.foundation.layout.ColumnScope  Canvas .androidx.compose.foundation.layout.ColumnScope  
CaptureButton .androidx.compose.foundation.layout.ColumnScope  CaptureModeSelector .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CenterFocusStrong .androidx.compose.foundation.layout.ColumnScope  Check .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  Close .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  ContentScale .androidx.compose.foundation.layout.ColumnScope  ContrastControl .androidx.compose.foundation.layout.ColumnScope  DropdownMenuItem .androidx.compose.foundation.layout.ColumnScope  EditTool .androidx.compose.foundation.layout.ColumnScope  EditToolbar .androidx.compose.foundation.layout.ColumnScope  Exposure .androidx.compose.foundation.layout.ColumnScope  
FilterChip .androidx.compose.foundation.layout.ColumnScope  FilterChipDefaults .androidx.compose.foundation.layout.ColumnScope  
FilterControl .androidx.compose.foundation.layout.ColumnScope  
FilterVintage .androidx.compose.foundation.layout.ColumnScope  	FlashAuto .androidx.compose.foundation.layout.ColumnScope  	FlashMode .androidx.compose.foundation.layout.ColumnScope  FlashOff .androidx.compose.foundation.layout.ColumnScope  FlashOn .androidx.compose.foundation.layout.ColumnScope  FlashOption .androidx.compose.foundation.layout.ColumnScope  Flip .androidx.compose.foundation.layout.ColumnScope  FlipCameraAndroid .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Grid3x3 .androidx.compose.foundation.layout.ColumnScope  HighQuality .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  ImageFilter .androidx.compose.foundation.layout.ColumnScope  ImageQuality .androidx.compose.foundation.layout.ColumnScope  Iso .androidx.compose.foundation.layout.ColumnScope  LazyRow .androidx.compose.foundation.layout.ColumnScope  
LocationOn .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  PhotoLibrary .androidx.compose.foundation.layout.ColumnScope  	PlayArrow .androidx.compose.foundation.layout.ColumnScope  ProfessionalControl .androidx.compose.foundation.layout.ColumnScope  RecordingStatus .androidx.compose.foundation.layout.ColumnScope  Refresh .androidx.compose.foundation.layout.ColumnScope  
RotateControl .androidx.compose.foundation.layout.ColumnScope  
RotateLeft .androidx.compose.foundation.layout.ColumnScope  RotateRight .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  SaturationControl .androidx.compose.foundation.layout.ColumnScope  SettingItem .androidx.compose.foundation.layout.ColumnScope  SettingsDropdown .androidx.compose.foundation.layout.ColumnScope  
SettingsPanel .androidx.compose.foundation.layout.ColumnScope  SettingsSlider .androidx.compose.foundation.layout.ColumnScope  SettingsSwitch .androidx.compose.foundation.layout.ColumnScope  Slider .androidx.compose.foundation.layout.ColumnScope  SliderDefaults .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  
StatusItem .androidx.compose.foundation.layout.ColumnScope  Stop .androidx.compose.foundation.layout.ColumnScope  String .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  Timer .androidx.compose.foundation.layout.ColumnScope  
TimerDelay .androidx.compose.foundation.layout.ColumnScope  Tune .androidx.compose.foundation.layout.ColumnScope  VideoQuality .androidx.compose.foundation.layout.ColumnScope  
VideoSettings .androidx.compose.foundation.layout.ColumnScope  VolumeUp .androidx.compose.foundation.layout.ColumnScope  WbSunny .androidx.compose.foundation.layout.ColumnScope  WhiteBalance .androidx.compose.foundation.layout.ColumnScope  androidx .androidx.compose.foundation.layout.ColumnScope  
asImageBitmap .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  colors .androidx.compose.foundation.layout.ColumnScope  content .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  filterChipColors .androidx.compose.foundation.layout.ColumnScope  forEach .androidx.compose.foundation.layout.ColumnScope  forEachIndexed .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  
formatTime .androidx.compose.foundation.layout.ColumnScope  getAllRatios .androidx.compose.foundation.layout.ColumnScope  getDisplayName .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  indexOf .androidx.compose.foundation.layout.ColumnScope  indexOfFirst .androidx.compose.foundation.layout.ColumnScope  invoke .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  map .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  rangeTo .androidx.compose.foundation.layout.ColumnScope  run .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  	Alignment +androidx.compose.foundation.layout.RowScope  	ArrowBack +androidx.compose.foundation.layout.RowScope  
ArrowDropDown +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  
CaptureButton +androidx.compose.foundation.layout.RowScope  CaptureMode +androidx.compose.foundation.layout.RowScope  CenterFocusWeak +androidx.compose.foundation.layout.RowScope  Check +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  Close +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  DropdownMenu +androidx.compose.foundation.layout.RowScope  DropdownMenuItem +androidx.compose.foundation.layout.RowScope  Edit +androidx.compose.foundation.layout.RowScope  
FilterChip +androidx.compose.foundation.layout.RowScope  FilterChipDefaults +androidx.compose.foundation.layout.RowScope  	FlashAuto +androidx.compose.foundation.layout.RowScope  	FlashMode +androidx.compose.foundation.layout.RowScope  FlashOff +androidx.compose.foundation.layout.RowScope  FlashOn +androidx.compose.foundation.layout.RowScope  Flip +androidx.compose.foundation.layout.RowScope  FlipCameraAndroid +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  Info +androidx.compose.foundation.layout.RowScope  LocalContentColor +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  PhotoLibrary +androidx.compose.foundation.layout.RowScope  	PlayArrow +androidx.compose.foundation.layout.RowScope  Refresh +androidx.compose.foundation.layout.RowScope  
RotateLeft +androidx.compose.foundation.layout.RowScope  RotateRight +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Settings +androidx.compose.foundation.layout.RowScope  Share +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  
StatusItem +androidx.compose.foundation.layout.RowScope  Stop +androidx.compose.foundation.layout.RowScope  Switch +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  WhiteBalance +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  buttonColors +androidx.compose.foundation.layout.RowScope  	clickable +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  filterChipColors +androidx.compose.foundation.layout.RowScope  forEach +androidx.compose.foundation.layout.RowScope  forEachIndexed +androidx.compose.foundation.layout.RowScope  
formatTime +androidx.compose.foundation.layout.RowScope  let +androidx.compose.foundation.layout.RowScope  listOf +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  to +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  compose +androidx.compose.foundation.layout.androidx  ui 3androidx.compose.foundation.layout.androidx.compose  graphics 6androidx.compose.foundation.layout.androidx.compose.ui  vector ?androidx.compose.foundation.layout.androidx.compose.ui.graphics  ImageVector Fandroidx.compose.foundation.layout.androidx.compose.ui.graphics.vector  example &androidx.compose.foundation.layout.com  google &androidx.compose.foundation.layout.com  tcs .androidx.compose.foundation.layout.com.example  data 2androidx.compose.foundation.layout.com.example.tcs  CameraSettings 7androidx.compose.foundation.layout.com.example.tcs.data  accompanist -androidx.compose.foundation.layout.com.google  permissions 9androidx.compose.foundation.layout.com.google.accompanist  MultiplePermissionsState Eandroidx.compose.foundation.layout.com.google.accompanist.permissions  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  AspectRatio .androidx.compose.foundation.lazy.LazyItemScope  AspectRatioHelper .androidx.compose.foundation.lazy.LazyItemScope  
CameraEnhance .androidx.compose.foundation.lazy.LazyItemScope  CameraFilter .androidx.compose.foundation.lazy.LazyItemScope  Exposure .androidx.compose.foundation.lazy.LazyItemScope  
FilterChip .androidx.compose.foundation.lazy.LazyItemScope  
FilterVintage .androidx.compose.foundation.lazy.LazyItemScope  FlipCameraAndroid .androidx.compose.foundation.lazy.LazyItemScope  Grid3x3 .androidx.compose.foundation.lazy.LazyItemScope  HighQuality .androidx.compose.foundation.lazy.LazyItemScope  Icons .androidx.compose.foundation.lazy.LazyItemScope  ImageQuality .androidx.compose.foundation.lazy.LazyItemScope  Iso .androidx.compose.foundation.lazy.LazyItemScope  
LocationOn .androidx.compose.foundation.lazy.LazyItemScope  SettingsDropdown .androidx.compose.foundation.lazy.LazyItemScope  SettingsSection .androidx.compose.foundation.lazy.LazyItemScope  SettingsSlider .androidx.compose.foundation.lazy.LazyItemScope  SettingsSwitch .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  Timer .androidx.compose.foundation.lazy.LazyItemScope  
TimerDelay .androidx.compose.foundation.lazy.LazyItemScope  
ToolButton .androidx.compose.foundation.lazy.LazyItemScope  Tune .androidx.compose.foundation.lazy.LazyItemScope  VideoQuality .androidx.compose.foundation.lazy.LazyItemScope  
VideoSettings .androidx.compose.foundation.lazy.LazyItemScope  VolumeUp .androidx.compose.foundation.lazy.LazyItemScope  WbSunny .androidx.compose.foundation.lazy.LazyItemScope  WhiteBalance .androidx.compose.foundation.lazy.LazyItemScope  getAllRatios .androidx.compose.foundation.lazy.LazyItemScope  getDisplayName .androidx.compose.foundation.lazy.LazyItemScope  indexOf .androidx.compose.foundation.lazy.LazyItemScope  indexOfFirst .androidx.compose.foundation.lazy.LazyItemScope  map .androidx.compose.foundation.lazy.LazyItemScope  rangeTo .androidx.compose.foundation.lazy.LazyItemScope  AspectRatio .androidx.compose.foundation.lazy.LazyListScope  AspectRatioHelper .androidx.compose.foundation.lazy.LazyListScope  
CameraEnhance .androidx.compose.foundation.lazy.LazyListScope  CameraFilter .androidx.compose.foundation.lazy.LazyListScope  EditTool .androidx.compose.foundation.lazy.LazyListScope  Exposure .androidx.compose.foundation.lazy.LazyListScope  
FilterChip .androidx.compose.foundation.lazy.LazyListScope  
FilterVintage .androidx.compose.foundation.lazy.LazyListScope  FlipCameraAndroid .androidx.compose.foundation.lazy.LazyListScope  Grid3x3 .androidx.compose.foundation.lazy.LazyListScope  HighQuality .androidx.compose.foundation.lazy.LazyListScope  Icons .androidx.compose.foundation.lazy.LazyListScope  ImageFilter .androidx.compose.foundation.lazy.LazyListScope  ImageQuality .androidx.compose.foundation.lazy.LazyListScope  Iso .androidx.compose.foundation.lazy.LazyListScope  
LocationOn .androidx.compose.foundation.lazy.LazyListScope  SettingsDropdown .androidx.compose.foundation.lazy.LazyListScope  SettingsSection .androidx.compose.foundation.lazy.LazyListScope  SettingsSlider .androidx.compose.foundation.lazy.LazyListScope  SettingsSwitch .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  Timer .androidx.compose.foundation.lazy.LazyListScope  
TimerDelay .androidx.compose.foundation.lazy.LazyListScope  
ToolButton .androidx.compose.foundation.lazy.LazyListScope  Tune .androidx.compose.foundation.lazy.LazyListScope  VideoQuality .androidx.compose.foundation.lazy.LazyListScope  
VideoSettings .androidx.compose.foundation.lazy.LazyListScope  VolumeUp .androidx.compose.foundation.lazy.LazyListScope  WbSunny .androidx.compose.foundation.lazy.LazyListScope  WhiteBalance .androidx.compose.foundation.lazy.LazyListScope  getAllRatios .androidx.compose.foundation.lazy.LazyListScope  getDisplayName .androidx.compose.foundation.lazy.LazyListScope  indexOf .androidx.compose.foundation.lazy.LazyListScope  indexOfFirst .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  map .androidx.compose.foundation.lazy.LazyListScope  rangeTo .androidx.compose.foundation.lazy.LazyListScope  	GridCells %androidx.compose.foundation.lazy.grid  LazyGridItemScope %androidx.compose.foundation.lazy.grid  
LazyGridScope %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  Fixed /androidx.compose.foundation.lazy.grid.GridCells  MediaThumbnail 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  MediaThumbnail 3androidx.compose.foundation.lazy.grid.LazyGridScope  items 3androidx.compose.foundation.lazy.grid.LazyGridScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  
ArrowDropDown ,androidx.compose.material.icons.Icons.Filled  AspectRatio ,androidx.compose.material.icons.Icons.Filled  Brightness6 ,androidx.compose.material.icons.Icons.Filled  Camera ,androidx.compose.material.icons.Icons.Filled  	CameraAlt ,androidx.compose.material.icons.Icons.Filled  
CameraEnhance ,androidx.compose.material.icons.Icons.Filled  CenterFocusStrong ,androidx.compose.material.icons.Icons.Filled  CenterFocusWeak ,androidx.compose.material.icons.Icons.Filled  Check ,androidx.compose.material.icons.Icons.Filled  Close ,androidx.compose.material.icons.Icons.Filled  Colorize ,androidx.compose.material.icons.Icons.Filled  Contrast ,androidx.compose.material.icons.Icons.Filled  Crop ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  Edit ,androidx.compose.material.icons.Icons.Filled  Exposure ,androidx.compose.material.icons.Icons.Filled  
FilterVintage ,androidx.compose.material.icons.Icons.Filled  	FlashAuto ,androidx.compose.material.icons.Icons.Filled  FlashOff ,androidx.compose.material.icons.Icons.Filled  FlashOn ,androidx.compose.material.icons.Icons.Filled  Flip ,androidx.compose.material.icons.Icons.Filled  FlipCameraAndroid ,androidx.compose.material.icons.Icons.Filled  Grid3x3 ,androidx.compose.material.icons.Icons.Filled  HighQuality ,androidx.compose.material.icons.Icons.Filled  Info ,androidx.compose.material.icons.Icons.Filled  Iso ,androidx.compose.material.icons.Icons.Filled  
LocationOn ,androidx.compose.material.icons.Icons.Filled  PhotoLibrary ,androidx.compose.material.icons.Icons.Filled  	PlayArrow ,androidx.compose.material.icons.Icons.Filled  Refresh ,androidx.compose.material.icons.Icons.Filled  
RotateLeft ,androidx.compose.material.icons.Icons.Filled  RotateRight ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  Share ,androidx.compose.material.icons.Icons.Filled  Stop ,androidx.compose.material.icons.Icons.Filled  Timer ,androidx.compose.material.icons.Icons.Filled  Tune ,androidx.compose.material.icons.Icons.Filled  
VideoSettings ,androidx.compose.material.icons.Icons.Filled  VolumeUp ,androidx.compose.material.icons.Icons.Filled  WbSunny ,androidx.compose.material.icons.Icons.Filled  AlertDialog &androidx.compose.material.icons.filled  	Alignment &androidx.compose.material.icons.filled  AndroidView &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  
ArrowDropDown &androidx.compose.material.icons.filled  AspectRatio &androidx.compose.material.icons.filled  AspectRatioHelper &androidx.compose.material.icons.filled  
AsyncImage &androidx.compose.material.icons.filled  Boolean &androidx.compose.material.icons.filled  BottomControlBar &androidx.compose.material.icons.filled  Box &androidx.compose.material.icons.filled  Brightness6 &androidx.compose.material.icons.filled  BrightnessControl &androidx.compose.material.icons.filled  Button &androidx.compose.material.icons.filled  ButtonDefaults &androidx.compose.material.icons.filled  Camera &androidx.compose.material.icons.filled  	CameraAlt &androidx.compose.material.icons.filled  
CameraEnhance &androidx.compose.material.icons.filled  CameraFilter &androidx.compose.material.icons.filled  CameraState &androidx.compose.material.icons.filled  CameraViewModel &androidx.compose.material.icons.filled  Canvas &androidx.compose.material.icons.filled  
CaptureButton &androidx.compose.material.icons.filled  CaptureMode &androidx.compose.material.icons.filled  CaptureModeSelector &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  CenterFocusStrong &androidx.compose.material.icons.filled  CenterFocusWeak &androidx.compose.material.icons.filled  Check &androidx.compose.material.icons.filled  CircleShape &androidx.compose.material.icons.filled  CircularProgressIndicator &androidx.compose.material.icons.filled  Close &androidx.compose.material.icons.filled  ClosedFloatingPointRange &androidx.compose.material.icons.filled  Color &androidx.compose.material.icons.filled  Colorize &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  ColumnScope &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  ContentScale &androidx.compose.material.icons.filled  Context &androidx.compose.material.icons.filled  Contrast &androidx.compose.material.icons.filled  ContrastControl &androidx.compose.material.icons.filled  Crop &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  DropdownMenu &androidx.compose.material.icons.filled  DropdownMenuItem &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  EditTool &androidx.compose.material.icons.filled  EditToolbar &androidx.compose.material.icons.filled  EmptyGalleryMessage &androidx.compose.material.icons.filled  	Exception &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  ExperimentalPermissionsApi &androidx.compose.material.icons.filled  Exposure &androidx.compose.material.icons.filled  
FilterChip &androidx.compose.material.icons.filled  FilterChipDefaults &androidx.compose.material.icons.filled  
FilterControl &androidx.compose.material.icons.filled  
FilterVintage &androidx.compose.material.icons.filled  	FlashAuto &androidx.compose.material.icons.filled  	FlashMode &androidx.compose.material.icons.filled  FlashOff &androidx.compose.material.icons.filled  FlashOn &androidx.compose.material.icons.filled  FlashOption &androidx.compose.material.icons.filled  FlashOptionsMenu &androidx.compose.material.icons.filled  Flip &androidx.compose.material.icons.filled  FlipCameraAndroid &androidx.compose.material.icons.filled  Float &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  GalleryViewModel &androidx.compose.material.icons.filled  Grid3x3 &androidx.compose.material.icons.filled  	GridCells &androidx.compose.material.icons.filled  HighQuality &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  
IconButton &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  ImageEditorViewModel &androidx.compose.material.icons.filled  ImageFilter &androidx.compose.material.icons.filled  ImageQuality &androidx.compose.material.icons.filled  ImageVector &androidx.compose.material.icons.filled  Info &androidx.compose.material.icons.filled  Int &androidx.compose.material.icons.filled  Iso &androidx.compose.material.icons.filled  LaunchedEffect &androidx.compose.material.icons.filled  LazyRow &androidx.compose.material.icons.filled  LazyVerticalGrid &androidx.compose.material.icons.filled  List &androidx.compose.material.icons.filled  LocalContentColor &androidx.compose.material.icons.filled  
LocationOn &androidx.compose.material.icons.filled  Long &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  	MediaFile &androidx.compose.material.icons.filled  MediaThumbnail &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  Offset &androidx.compose.material.icons.filled  OptIn &androidx.compose.material.icons.filled  
PaddingValues &androidx.compose.material.icons.filled  PermissionUtils &androidx.compose.material.icons.filled  PhotoLibrary &androidx.compose.material.icons.filled  	PlayArrow &androidx.compose.material.icons.filled  PreviewView &androidx.compose.material.icons.filled  ProfessionalControl &androidx.compose.material.icons.filled  RecordingStatus &androidx.compose.material.icons.filled  Refresh &androidx.compose.material.icons.filled  
RotateControl &androidx.compose.material.icons.filled  
RotateLeft &androidx.compose.material.icons.filled  RotateRight &androidx.compose.material.icons.filled  RoundedCornerShape &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  SaturationControl &androidx.compose.material.icons.filled  Scaffold &androidx.compose.material.icons.filled  SettingItem &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  SettingsDropdown &androidx.compose.material.icons.filled  
SettingsPanel &androidx.compose.material.icons.filled  SettingsSection &androidx.compose.material.icons.filled  SettingsSlider &androidx.compose.material.icons.filled  SettingsSwitch &androidx.compose.material.icons.filled  Share &androidx.compose.material.icons.filled  
ShareUtils &androidx.compose.material.icons.filled  Slider &androidx.compose.material.icons.filled  SliderDefaults &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  
StatusItem &androidx.compose.material.icons.filled  Stop &androidx.compose.material.icons.filled  String &androidx.compose.material.icons.filled  Switch &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  
TextButton &androidx.compose.material.icons.filled  TimeLapseSettings &androidx.compose.material.icons.filled  Timer &androidx.compose.material.icons.filled  
TimerDelay &androidx.compose.material.icons.filled  
ToolButton &androidx.compose.material.icons.filled  	TopAppBar &androidx.compose.material.icons.filled  
TopControlBar &androidx.compose.material.icons.filled  Tune &androidx.compose.material.icons.filled  Unit &androidx.compose.material.icons.filled  Uri &androidx.compose.material.icons.filled  VideoQuality &androidx.compose.material.icons.filled  
VideoSettings &androidx.compose.material.icons.filled  VolumeUp &androidx.compose.material.icons.filled  WbSunny &androidx.compose.material.icons.filled  WhiteBalance &androidx.compose.material.icons.filled  align &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  apply &androidx.compose.material.icons.filled  
asImageBitmap &androidx.compose.material.icons.filled  aspectRatio &androidx.compose.material.icons.filled  
background &androidx.compose.material.icons.filled  buttonColors &androidx.compose.material.icons.filled  
cardColors &androidx.compose.material.icons.filled  
cardElevation &androidx.compose.material.icons.filled  	clickable &androidx.compose.material.icons.filled  collectAsState &androidx.compose.material.icons.filled  colors &androidx.compose.material.icons.filled  com &androidx.compose.material.icons.filled  delay &androidx.compose.material.icons.filled  deleteImage &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  filterChipColors &androidx.compose.material.icons.filled  forEach &androidx.compose.material.icons.filled  forEachIndexed &androidx.compose.material.icons.filled  format &androidx.compose.material.icons.filled  formatDuration &androidx.compose.material.icons.filled  
formatTime &androidx.compose.material.icons.filled  getAllRatios &androidx.compose.material.icons.filled  getCameraPermissions &androidx.compose.material.icons.filled  getDisplayName &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  
graphicsLayer &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  indexOf &androidx.compose.material.icons.filled  indexOfFirst &androidx.compose.material.icons.filled  let &androidx.compose.material.icons.filled  listOf &androidx.compose.material.icons.filled  map &androidx.compose.material.icons.filled  mutableStateOf &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  
plusAssign &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  rangeTo &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  run &androidx.compose.material.icons.filled  setValue &androidx.compose.material.icons.filled  
shareImage &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  spacedBy &androidx.compose.material.icons.filled  timesAssign &androidx.compose.material.icons.filled  to &androidx.compose.material.icons.filled  toList &androidx.compose.material.icons.filled  
transformable &androidx.compose.material.icons.filled  weight &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  compose /androidx.compose.material.icons.filled.androidx  ui 7androidx.compose.material.icons.filled.androidx.compose  graphics :androidx.compose.material.icons.filled.androidx.compose.ui  vector Candroidx.compose.material.icons.filled.androidx.compose.ui.graphics  ImageVector Jandroidx.compose.material.icons.filled.androidx.compose.ui.graphics.vector  example *androidx.compose.material.icons.filled.com  google *androidx.compose.material.icons.filled.com  tcs 2androidx.compose.material.icons.filled.com.example  data 6androidx.compose.material.icons.filled.com.example.tcs  CameraSettings ;androidx.compose.material.icons.filled.com.example.tcs.data  accompanist 1androidx.compose.material.icons.filled.com.google  permissions =androidx.compose.material.icons.filled.com.google.accompanist  MultiplePermissionsState Iandroidx.compose.material.icons.filled.com.google.accompanist.permissions  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  AndroidView androidx.compose.material3  Arrangement androidx.compose.material3  	ArrowBack androidx.compose.material3  
ArrowDropDown androidx.compose.material3  AspectRatio androidx.compose.material3  AspectRatioHelper androidx.compose.material3  
AsyncImage androidx.compose.material3  Boolean androidx.compose.material3  BottomControlBar androidx.compose.material3  Box androidx.compose.material3  Brightness6 androidx.compose.material3  BrightnessControl androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Camera androidx.compose.material3  	CameraAlt androidx.compose.material3  
CameraEnhance androidx.compose.material3  CameraFilter androidx.compose.material3  CameraState androidx.compose.material3  CameraViewModel androidx.compose.material3  Canvas androidx.compose.material3  
CaptureButton androidx.compose.material3  CaptureMode androidx.compose.material3  CaptureModeSelector androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  CenterFocusStrong androidx.compose.material3  CenterFocusWeak androidx.compose.material3  Check androidx.compose.material3  CircleShape androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Close androidx.compose.material3  ClosedFloatingPointRange androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Colorize androidx.compose.material3  Column androidx.compose.material3  ColumnScope androidx.compose.material3  
Composable androidx.compose.material3  ContentScale androidx.compose.material3  Context androidx.compose.material3  Contrast androidx.compose.material3  ContrastControl androidx.compose.material3  Crop androidx.compose.material3  Delete androidx.compose.material3  DropdownMenu androidx.compose.material3  DropdownMenuItem androidx.compose.material3  Edit androidx.compose.material3  EditTool androidx.compose.material3  EditToolbar androidx.compose.material3  EmptyGalleryMessage androidx.compose.material3  	Exception androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  ExperimentalPermissionsApi androidx.compose.material3  Exposure androidx.compose.material3  
FilterChip androidx.compose.material3  FilterChipDefaults androidx.compose.material3  
FilterControl androidx.compose.material3  
FilterVintage androidx.compose.material3  	FlashAuto androidx.compose.material3  	FlashMode androidx.compose.material3  FlashOff androidx.compose.material3  FlashOn androidx.compose.material3  FlashOption androidx.compose.material3  FlashOptionsMenu androidx.compose.material3  Flip androidx.compose.material3  FlipCameraAndroid androidx.compose.material3  Float androidx.compose.material3  
FontWeight androidx.compose.material3  GalleryViewModel androidx.compose.material3  Grid3x3 androidx.compose.material3  	GridCells androidx.compose.material3  HighQuality androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  ImageEditorViewModel androidx.compose.material3  ImageFilter androidx.compose.material3  ImageQuality androidx.compose.material3  ImageVector androidx.compose.material3  Info androidx.compose.material3  Int androidx.compose.material3  Iso androidx.compose.material3  LaunchedEffect androidx.compose.material3  LazyRow androidx.compose.material3  LazyVerticalGrid androidx.compose.material3  List androidx.compose.material3  LocalContentColor androidx.compose.material3  
LocationOn androidx.compose.material3  Long androidx.compose.material3  
MaterialTheme androidx.compose.material3  	MediaFile androidx.compose.material3  MediaThumbnail androidx.compose.material3  Modifier androidx.compose.material3  Offset androidx.compose.material3  OptIn androidx.compose.material3  
PaddingValues androidx.compose.material3  PermissionUtils androidx.compose.material3  PhotoLibrary androidx.compose.material3  	PlayArrow androidx.compose.material3  PreviewView androidx.compose.material3  ProfessionalControl androidx.compose.material3  RecordingStatus androidx.compose.material3  Refresh androidx.compose.material3  
RotateControl androidx.compose.material3  
RotateLeft androidx.compose.material3  RotateRight androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  SaturationControl androidx.compose.material3  Scaffold androidx.compose.material3  SelectableChipColors androidx.compose.material3  SettingItem androidx.compose.material3  Settings androidx.compose.material3  SettingsDropdown androidx.compose.material3  
SettingsPanel androidx.compose.material3  SettingsSection androidx.compose.material3  SettingsSlider androidx.compose.material3  SettingsSwitch androidx.compose.material3  Share androidx.compose.material3  
ShareUtils androidx.compose.material3  Slider androidx.compose.material3  SliderColors androidx.compose.material3  SliderDefaults androidx.compose.material3  SnackbarHostState androidx.compose.material3  SnackbarResult androidx.compose.material3  Spacer androidx.compose.material3  
StatusItem androidx.compose.material3  Stop androidx.compose.material3  String androidx.compose.material3  Switch androidx.compose.material3  Text androidx.compose.material3  
TextButton androidx.compose.material3  TimeLapseSettings androidx.compose.material3  Timer androidx.compose.material3  
TimerDelay androidx.compose.material3  
ToolButton androidx.compose.material3  	TopAppBar androidx.compose.material3  
TopControlBar androidx.compose.material3  Tune androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  Uri androidx.compose.material3  VideoQuality androidx.compose.material3  
VideoSettings androidx.compose.material3  VolumeUp androidx.compose.material3  WbSunny androidx.compose.material3  WhiteBalance androidx.compose.material3  align androidx.compose.material3  androidx androidx.compose.material3  apply androidx.compose.material3  
asImageBitmap androidx.compose.material3  aspectRatio androidx.compose.material3  
background androidx.compose.material3  buttonColors androidx.compose.material3  
cardColors androidx.compose.material3  
cardElevation androidx.compose.material3  	clickable androidx.compose.material3  collectAsState androidx.compose.material3  colors androidx.compose.material3  com androidx.compose.material3  darkColorScheme androidx.compose.material3  delay androidx.compose.material3  deleteImage androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  filterChipColors androidx.compose.material3  forEach androidx.compose.material3  forEachIndexed androidx.compose.material3  format androidx.compose.material3  formatDuration androidx.compose.material3  
formatTime androidx.compose.material3  getAllRatios androidx.compose.material3  getCameraPermissions androidx.compose.material3  getDisplayName androidx.compose.material3  getValue androidx.compose.material3  
graphicsLayer androidx.compose.material3  height androidx.compose.material3  indexOf androidx.compose.material3  indexOfFirst androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  map androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  
plusAssign androidx.compose.material3  provideDelegate androidx.compose.material3  rangeTo androidx.compose.material3  remember androidx.compose.material3  run androidx.compose.material3  setValue androidx.compose.material3  
shareImage androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  timesAssign androidx.compose.material3  to androidx.compose.material3  toList androidx.compose.material3  
transformable androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  errorContainer &androidx.compose.material3.ColorScheme  onErrorContainer &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  filterChipColors -androidx.compose.material3.FilterChipDefaults  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  colors )androidx.compose.material3.SliderDefaults  showSnackbar ,androidx.compose.material3.SnackbarHostState  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
labelSmall %androidx.compose.material3.Typography  
titleLarge %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  compose #androidx.compose.material3.androidx  ui +androidx.compose.material3.androidx.compose  graphics .androidx.compose.material3.androidx.compose.ui  vector 7androidx.compose.material3.androidx.compose.ui.graphics  ImageVector >androidx.compose.material3.androidx.compose.ui.graphics.vector  example androidx.compose.material3.com  google androidx.compose.material3.com  tcs &androidx.compose.material3.com.example  data *androidx.compose.material3.com.example.tcs  CameraSettings /androidx.compose.material3.com.example.tcs.data  accompanist %androidx.compose.material3.com.google  permissions 1androidx.compose.material3.com.google.accompanist  MultiplePermissionsState =androidx.compose.material3.com.google.accompanist.permissions  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  AndroidView androidx.compose.runtime  Arrangement androidx.compose.runtime  	ArrowBack androidx.compose.runtime  
ArrowDropDown androidx.compose.runtime  AspectRatio androidx.compose.runtime  AspectRatioHelper androidx.compose.runtime  
AsyncImage androidx.compose.runtime  Boolean androidx.compose.runtime  BottomControlBar androidx.compose.runtime  Box androidx.compose.runtime  Brightness6 androidx.compose.runtime  BrightnessControl androidx.compose.runtime  Bundle androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Camera androidx.compose.runtime  	CameraAlt androidx.compose.runtime  	CameraApp androidx.compose.runtime  
CameraEnhance androidx.compose.runtime  CameraFilter androidx.compose.runtime  CameraPreviewScreen androidx.compose.runtime  CameraSettingsScreen androidx.compose.runtime  CameraState androidx.compose.runtime  CameraViewModel androidx.compose.runtime  Canvas androidx.compose.runtime  
CaptureButton androidx.compose.runtime  CaptureMode androidx.compose.runtime  CaptureModeSelector androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CenterFocusStrong androidx.compose.runtime  CenterFocusWeak androidx.compose.runtime  Check androidx.compose.runtime  CircleShape androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Close androidx.compose.runtime  ClosedFloatingPointRange androidx.compose.runtime  Color androidx.compose.runtime  Colorize androidx.compose.runtime  Column androidx.compose.runtime  ColumnScope androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  ContentScale androidx.compose.runtime  Context androidx.compose.runtime  Contrast androidx.compose.runtime  ContrastControl androidx.compose.runtime  Crop androidx.compose.runtime  Delete androidx.compose.runtime  DropdownMenu androidx.compose.runtime  DropdownMenuItem androidx.compose.runtime  Edit androidx.compose.runtime  EditTool androidx.compose.runtime  EditToolbar androidx.compose.runtime  EmptyGalleryMessage androidx.compose.runtime  	Exception androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  ExperimentalPermissionsApi androidx.compose.runtime  Exposure androidx.compose.runtime  
FilterChip androidx.compose.runtime  FilterChipDefaults androidx.compose.runtime  
FilterControl androidx.compose.runtime  
FilterVintage androidx.compose.runtime  	FlashAuto androidx.compose.runtime  	FlashMode androidx.compose.runtime  FlashOff androidx.compose.runtime  FlashOn androidx.compose.runtime  FlashOption androidx.compose.runtime  FlashOptionsMenu androidx.compose.runtime  Flip androidx.compose.runtime  FlipCameraAndroid androidx.compose.runtime  Float androidx.compose.runtime  
FontWeight androidx.compose.runtime  
GalleryScreen androidx.compose.runtime  GalleryViewModel androidx.compose.runtime  Grid3x3 androidx.compose.runtime  	GridCells androidx.compose.runtime  HighQuality androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  ImageEditorScreen androidx.compose.runtime  ImageEditorViewModel androidx.compose.runtime  ImageFilter androidx.compose.runtime  ImageQuality androidx.compose.runtime  ImageVector androidx.compose.runtime  ImageViewerScreen androidx.compose.runtime  Info androidx.compose.runtime  Int androidx.compose.runtime  Iso androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  LazyRow androidx.compose.runtime  LazyVerticalGrid androidx.compose.runtime  List androidx.compose.runtime  LocalContentColor androidx.compose.runtime  
LocationOn androidx.compose.runtime  Long androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  	MediaFile androidx.compose.runtime  MediaThumbnail androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  Offset androidx.compose.runtime  OptIn androidx.compose.runtime  
PaddingValues androidx.compose.runtime  PermissionUtils androidx.compose.runtime  PhotoLibrary androidx.compose.runtime  	PlayArrow androidx.compose.runtime  PreviewView androidx.compose.runtime  ProfessionalControl androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  RecordingStatus androidx.compose.runtime  Refresh androidx.compose.runtime  
RotateControl androidx.compose.runtime  
RotateLeft androidx.compose.runtime  RotateRight androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  SaturationControl androidx.compose.runtime  Scaffold androidx.compose.runtime  SettingItem androidx.compose.runtime  Settings androidx.compose.runtime  SettingsDropdown androidx.compose.runtime  
SettingsPanel androidx.compose.runtime  SettingsSection androidx.compose.runtime  SettingsSlider androidx.compose.runtime  SettingsSwitch androidx.compose.runtime  Share androidx.compose.runtime  
ShareUtils androidx.compose.runtime  Slider androidx.compose.runtime  SliderDefaults androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  
StatusItem androidx.compose.runtime  Stop androidx.compose.runtime  String androidx.compose.runtime  Switch androidx.compose.runtime  TcsTheme androidx.compose.runtime  Text androidx.compose.runtime  
TextButton androidx.compose.runtime  TimeLapseSettings androidx.compose.runtime  Timer androidx.compose.runtime  
TimerDelay androidx.compose.runtime  
ToolButton androidx.compose.runtime  	TopAppBar androidx.compose.runtime  
TopControlBar androidx.compose.runtime  Tune androidx.compose.runtime  Unit androidx.compose.runtime  Uri androidx.compose.runtime  VideoQuality androidx.compose.runtime  
VideoSettings androidx.compose.runtime  VolumeUp androidx.compose.runtime  WbSunny androidx.compose.runtime  WhiteBalance androidx.compose.runtime  align androidx.compose.runtime  androidx androidx.compose.runtime  apply androidx.compose.runtime  
asImageBitmap androidx.compose.runtime  aspectRatio androidx.compose.runtime  
background androidx.compose.runtime  buttonColors androidx.compose.runtime  
cardColors androidx.compose.runtime  
cardElevation androidx.compose.runtime  	clickable androidx.compose.runtime  collectAsState androidx.compose.runtime  colors androidx.compose.runtime  com androidx.compose.runtime  delay androidx.compose.runtime  deleteImage androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  filterChipColors androidx.compose.runtime  forEach androidx.compose.runtime  forEachIndexed androidx.compose.runtime  format androidx.compose.runtime  formatDuration androidx.compose.runtime  
formatTime androidx.compose.runtime  getAllRatios androidx.compose.runtime  getCameraPermissions androidx.compose.runtime  getDisplayName androidx.compose.runtime  getValue androidx.compose.runtime  
graphicsLayer androidx.compose.runtime  height androidx.compose.runtime  indexOf androidx.compose.runtime  indexOfFirst androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  map androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  
plusAssign androidx.compose.runtime  provideDelegate androidx.compose.runtime  rangeTo androidx.compose.runtime  remember androidx.compose.runtime  run androidx.compose.runtime  setValue androidx.compose.runtime  
shareImage androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  timesAssign androidx.compose.runtime  to androidx.compose.runtime  toList androidx.compose.runtime  
transformable androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  compose !androidx.compose.runtime.androidx  ui )androidx.compose.runtime.androidx.compose  graphics ,androidx.compose.runtime.androidx.compose.ui  vector 5androidx.compose.runtime.androidx.compose.ui.graphics  ImageVector <androidx.compose.runtime.androidx.compose.ui.graphics.vector  example androidx.compose.runtime.com  google androidx.compose.runtime.com  tcs $androidx.compose.runtime.com.example  data (androidx.compose.runtime.com.example.tcs  CameraSettings -androidx.compose.runtime.com.example.tcs.data  accompanist #androidx.compose.runtime.com.google  permissions /androidx.compose.runtime.com.google.accompanist  MultiplePermissionsState ;androidx.compose.runtime.com.google.accompanist.permissions  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  invoke 5androidx.compose.runtime.internal.ComposableFunction0  invoke 5androidx.compose.runtime.internal.ComposableFunction1  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  BottomCenter androidx.compose.ui.Alignment  	BottomEnd androidx.compose.ui.Alignment  BottomStart androidx.compose.ui.Alignment  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  	TopCenter androidx.compose.ui.Alignment  TopEnd androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  BottomCenter 'androidx.compose.ui.Alignment.Companion  	BottomEnd 'androidx.compose.ui.Alignment.Companion  BottomStart 'androidx.compose.ui.Alignment.Companion  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	TopCenter 'androidx.compose.ui.Alignment.Companion  TopEnd 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  aspectRatio androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  
graphicsLayer androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  
transformable androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  aspectRatio &androidx.compose.ui.Modifier.Companion  
background &androidx.compose.ui.Modifier.Companion  	clickable &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Offset androidx.compose.ui.geometry  Size androidx.compose.ui.geometry  	Companion #androidx.compose.ui.geometry.Offset  Zero #androidx.compose.ui.geometry.Offset  plus #androidx.compose.ui.geometry.Offset  
plusAssign #androidx.compose.ui.geometry.Offset  x #androidx.compose.ui.geometry.Offset  y #androidx.compose.ui.geometry.Offset  Zero -androidx.compose.ui.geometry.Offset.Companion  height !androidx.compose.ui.geometry.Size  width !androidx.compose.ui.geometry.Size  Color androidx.compose.ui.graphics  ImageBitmap androidx.compose.ui.graphics  
asImageBitmap androidx.compose.ui.graphics  
graphicsLayer androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  Red "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Red ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  	DrawScope &androidx.compose.ui.graphics.drawscope  androidx 0androidx.compose.ui.graphics.drawscope.DrawScope  
asImageBitmap 0androidx.compose.ui.graphics.drawscope.DrawScope  	drawImage 0androidx.compose.ui.graphics.drawscope.DrawScope  size 0androidx.compose.ui.graphics.drawscope.DrawScope  ImageVector #androidx.compose.ui.graphics.vector  ContentScale androidx.compose.ui.layout  	Companion 'androidx.compose.ui.layout.ContentScale  Crop 'androidx.compose.ui.layout.ContentScale  Fit 'androidx.compose.ui.layout.ContentScale  Crop 1androidx.compose.ui.layout.ContentScale.Companion  Fit 1androidx.compose.ui.layout.ContentScale.Companion  LocalContext androidx.compose.ui.platform  LocalLifecycleOwner androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  IntSize androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  AndroidView androidx.compose.ui.viewinterop  Bundle #androidx.core.app.ComponentActivity  	CameraApp #androidx.core.app.ComponentActivity  TcsTheme #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  FileProvider androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  getMainExecutor #androidx.core.content.ContextCompat  
getUriForFile "androidx.core.content.FileProvider  Consumer androidx.core.util  <SAM-CONSTRUCTOR> androidx.core.util.Consumer  AndroidViewModel androidx.lifecycle  LifecycleOwner androidx.lifecycle  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  	onCleared #androidx.lifecycle.AndroidViewModel  	onCleared androidx.lifecycle.ViewModel  	viewModel $androidx.lifecycle.viewmodel.compose  NavBackStackEntry androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  navigate !androidx.navigation.NavController  popBackStack !androidx.navigation.NavController  CameraPreviewScreen #androidx.navigation.NavGraphBuilder  CameraSettingsScreen #androidx.navigation.NavGraphBuilder  
GalleryScreen #androidx.navigation.NavGraphBuilder  ImageEditorScreen #androidx.navigation.NavGraphBuilder  ImageViewerScreen #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  let #androidx.navigation.NavGraphBuilder  navigate %androidx.navigation.NavHostController  popBackStack %androidx.navigation.NavHostController  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  
AsyncImage coil.compose  Bundle com.example.tcs  	CameraApp com.example.tcs  CameraPreviewScreen com.example.tcs  CameraSettingsScreen com.example.tcs  ComponentActivity com.example.tcs  
Composable com.example.tcs  
GalleryScreen com.example.tcs  ImageEditorScreen com.example.tcs  ImageViewerScreen com.example.tcs  MainActivity com.example.tcs  TcsTheme com.example.tcs  Uri com.example.tcs  getValue com.example.tcs  let com.example.tcs  mutableStateOf com.example.tcs  provideDelegate com.example.tcs  remember com.example.tcs  setValue com.example.tcs  	CameraApp com.example.tcs.MainActivity  TcsTheme com.example.tcs.MainActivity  enableEdgeToEdge com.example.tcs.MainActivity  
setContent com.example.tcs.MainActivity  Boolean com.example.tcs.camera  Camera com.example.tcs.camera  CameraController com.example.tcs.camera  CameraSelector com.example.tcs.camera  CameraSettings com.example.tcs.camera  CameraState com.example.tcs.camera  Context com.example.tcs.camera  
ContextCompat com.example.tcs.camera  	Exception com.example.tcs.camera  ExecutorService com.example.tcs.camera  	Executors com.example.tcs.camera  FILENAME_FORMAT com.example.tcs.camera  File com.example.tcs.camera  FileOutputOptions com.example.tcs.camera  ImageCapture com.example.tcs.camera  ImageCaptureException com.example.tcs.camera  LifecycleOwner com.example.tcs.camera  Locale com.example.tcs.camera  Log com.example.tcs.camera  MutableStateFlow com.example.tcs.camera  Preview com.example.tcs.camera  PreviewView com.example.tcs.camera  ProcessCameraProvider com.example.tcs.camera  Quality com.example.tcs.camera  QualitySelector com.example.tcs.camera  Recorder com.example.tcs.camera  	Recording com.example.tcs.camera  SimpleDateFormat com.example.tcs.camera  	StateFlow com.example.tcs.camera  String com.example.tcs.camera  System com.example.tcs.camera  TAG com.example.tcs.camera  Unit com.example.tcs.camera  Uri com.example.tcs.camera  VideoCapture com.example.tcs.camera  VideoRecordEvent com.example.tcs.camera  _cameraState com.example.tcs.camera  also com.example.tcs.camera  asStateFlow com.example.tcs.camera  run com.example.tcs.camera  Boolean 'com.example.tcs.camera.CameraController  Camera 'com.example.tcs.camera.CameraController  CameraSelector 'com.example.tcs.camera.CameraController  CameraSettings 'com.example.tcs.camera.CameraController  CameraState 'com.example.tcs.camera.CameraController  Context 'com.example.tcs.camera.CameraController  
ContextCompat 'com.example.tcs.camera.CameraController  	Exception 'com.example.tcs.camera.CameraController  ExecutorService 'com.example.tcs.camera.CameraController  	Executors 'com.example.tcs.camera.CameraController  FILENAME_FORMAT 'com.example.tcs.camera.CameraController  File 'com.example.tcs.camera.CameraController  FileOutputOptions 'com.example.tcs.camera.CameraController  ImageCapture 'com.example.tcs.camera.CameraController  ImageCaptureException 'com.example.tcs.camera.CameraController  LifecycleOwner 'com.example.tcs.camera.CameraController  Locale 'com.example.tcs.camera.CameraController  Log 'com.example.tcs.camera.CameraController  MutableStateFlow 'com.example.tcs.camera.CameraController  Preview 'com.example.tcs.camera.CameraController  PreviewView 'com.example.tcs.camera.CameraController  ProcessCameraProvider 'com.example.tcs.camera.CameraController  Quality 'com.example.tcs.camera.CameraController  QualitySelector 'com.example.tcs.camera.CameraController  Recorder 'com.example.tcs.camera.CameraController  	Recording 'com.example.tcs.camera.CameraController  SimpleDateFormat 'com.example.tcs.camera.CameraController  	StateFlow 'com.example.tcs.camera.CameraController  String 'com.example.tcs.camera.CameraController  System 'com.example.tcs.camera.CameraController  TAG 'com.example.tcs.camera.CameraController  Unit 'com.example.tcs.camera.CameraController  Uri 'com.example.tcs.camera.CameraController  VideoCapture 'com.example.tcs.camera.CameraController  VideoRecordEvent 'com.example.tcs.camera.CameraController  _cameraState 'com.example.tcs.camera.CameraController  _error 'com.example.tcs.camera.CameraController  	_settings 'com.example.tcs.camera.CameraController  also 'com.example.tcs.camera.CameraController  asStateFlow 'com.example.tcs.camera.CameraController  camera 'com.example.tcs.camera.CameraController  cameraExecutor 'com.example.tcs.camera.CameraController  cameraProvider 'com.example.tcs.camera.CameraController  cameraState 'com.example.tcs.camera.CameraController  context 'com.example.tcs.camera.CameraController  error 'com.example.tcs.camera.CameraController  imageCapture 'com.example.tcs.camera.CameraController  initializeCamera 'com.example.tcs.camera.CameraController  preview 'com.example.tcs.camera.CameraController  	recording 'com.example.tcs.camera.CameraController  release 'com.example.tcs.camera.CameraController  run 'com.example.tcs.camera.CameraController  settings 'com.example.tcs.camera.CameraController  setupCamera 'com.example.tcs.camera.CameraController  startVideoRecording 'com.example.tcs.camera.CameraController  stopVideoRecording 'com.example.tcs.camera.CameraController  switchCamera 'com.example.tcs.camera.CameraController  	takePhoto 'com.example.tcs.camera.CameraController  updateSettings 'com.example.tcs.camera.CameraController  videoCapture 'com.example.tcs.camera.CameraController  CameraSelector 1com.example.tcs.camera.CameraController.Companion  CameraSettings 1com.example.tcs.camera.CameraController.Companion  CameraState 1com.example.tcs.camera.CameraController.Companion  
ContextCompat 1com.example.tcs.camera.CameraController.Companion  	Executors 1com.example.tcs.camera.CameraController.Companion  FILENAME_FORMAT 1com.example.tcs.camera.CameraController.Companion  File 1com.example.tcs.camera.CameraController.Companion  FileOutputOptions 1com.example.tcs.camera.CameraController.Companion  ImageCapture 1com.example.tcs.camera.CameraController.Companion  Locale 1com.example.tcs.camera.CameraController.Companion  Log 1com.example.tcs.camera.CameraController.Companion  MutableStateFlow 1com.example.tcs.camera.CameraController.Companion  Preview 1com.example.tcs.camera.CameraController.Companion  ProcessCameraProvider 1com.example.tcs.camera.CameraController.Companion  Quality 1com.example.tcs.camera.CameraController.Companion  QualitySelector 1com.example.tcs.camera.CameraController.Companion  Recorder 1com.example.tcs.camera.CameraController.Companion  SimpleDateFormat 1com.example.tcs.camera.CameraController.Companion  System 1com.example.tcs.camera.CameraController.Companion  TAG 1com.example.tcs.camera.CameraController.Companion  Uri 1com.example.tcs.camera.CameraController.Companion  VideoCapture 1com.example.tcs.camera.CameraController.Companion  _cameraState 1com.example.tcs.camera.CameraController.Companion  also 1com.example.tcs.camera.CameraController.Companion  asStateFlow 1com.example.tcs.camera.CameraController.Companion  run 1com.example.tcs.camera.CameraController.Companion  OnImageSavedCallback 4com.example.tcs.camera.CameraController.ImageCapture  OutputFileResults 4com.example.tcs.camera.CameraController.ImageCapture  Finalize 8com.example.tcs.camera.CameraController.VideoRecordEvent  Start 8com.example.tcs.camera.CameraController.VideoRecordEvent  	CAPTURING "com.example.tcs.camera.CameraState  ERROR "com.example.tcs.camera.CameraState  IDLE "com.example.tcs.camera.CameraState  INITIALIZING "com.example.tcs.camera.CameraState  READY "com.example.tcs.camera.CameraState  	RECORDING "com.example.tcs.camera.CameraState  OnImageSavedCallback #com.example.tcs.camera.ImageCapture  OutputFileResults #com.example.tcs.camera.ImageCapture  Finalize 'com.example.tcs.camera.VideoRecordEvent  Start 'com.example.tcs.camera.VideoRecordEvent  	Alignment com.example.tcs.data  Arrangement com.example.tcs.data  	ArrowBack com.example.tcs.data  
ArrowDropDown com.example.tcs.data  AspectRatio com.example.tcs.data  AspectRatioHelper com.example.tcs.data  Boolean com.example.tcs.data  Box com.example.tcs.data  
CameraEnhance com.example.tcs.data  CameraFilter com.example.tcs.data  CameraSettings com.example.tcs.data  CameraViewModel com.example.tcs.data  CaptureMode com.example.tcs.data  Card com.example.tcs.data  CardDefaults com.example.tcs.data  Check com.example.tcs.data  ClosedFloatingPointRange com.example.tcs.data  Column com.example.tcs.data  ColumnScope com.example.tcs.data  
Composable com.example.tcs.data  DropdownMenu com.example.tcs.data  DropdownMenuItem com.example.tcs.data  ExperimentalMaterial3Api com.example.tcs.data  Exposure com.example.tcs.data  
FilterVintage com.example.tcs.data  	FlashMode com.example.tcs.data  FlipCameraAndroid com.example.tcs.data  Float com.example.tcs.data  Grid3x3 com.example.tcs.data  HighQuality com.example.tcs.data  Icon com.example.tcs.data  
IconButton com.example.tcs.data  Icons com.example.tcs.data  ImageCapture com.example.tcs.data  ImageQuality com.example.tcs.data  ImageVector com.example.tcs.data  Int com.example.tcs.data  Iso com.example.tcs.data  List com.example.tcs.data  
LocationOn com.example.tcs.data  
MaterialTheme com.example.tcs.data  Modifier com.example.tcs.data  OptIn com.example.tcs.data  
PaddingValues com.example.tcs.data  Pair com.example.tcs.data  Row com.example.tcs.data  Scaffold com.example.tcs.data  SettingsDropdown com.example.tcs.data  SettingsSection com.example.tcs.data  SettingsSlider com.example.tcs.data  SettingsSwitch com.example.tcs.data  Slider com.example.tcs.data  Spacer com.example.tcs.data  String com.example.tcs.data  Switch com.example.tcs.data  Text com.example.tcs.data  
TextButton com.example.tcs.data  Timer com.example.tcs.data  
TimerDelay com.example.tcs.data  	TopAppBar com.example.tcs.data  Tune com.example.tcs.data  Unit com.example.tcs.data  VideoQuality com.example.tcs.data  
VideoSettings com.example.tcs.data  VolumeUp com.example.tcs.data  WbSunny com.example.tcs.data  WhiteBalance com.example.tcs.data  androidx com.example.tcs.data  
cardElevation com.example.tcs.data  collectAsState com.example.tcs.data  fillMaxSize com.example.tcs.data  fillMaxWidth com.example.tcs.data  forEachIndexed com.example.tcs.data  getAllRatios com.example.tcs.data  getDisplayName com.example.tcs.data  getValue com.example.tcs.data  height com.example.tcs.data  indexOf com.example.tcs.data  indexOfFirst com.example.tcs.data  listOf com.example.tcs.data  map com.example.tcs.data  mutableStateOf com.example.tcs.data  padding com.example.tcs.data  provideDelegate com.example.tcs.data  rangeTo com.example.tcs.data  remember com.example.tcs.data  setValue com.example.tcs.data  size com.example.tcs.data  spacedBy com.example.tcs.data  to com.example.tcs.data  weight com.example.tcs.data  width com.example.tcs.data  AspectRatio &com.example.tcs.data.AspectRatioHelper  getAllRatios &com.example.tcs.data.AspectRatioHelper  getDisplayName &com.example.tcs.data.AspectRatioHelper  listOf &com.example.tcs.data.AspectRatioHelper  to &com.example.tcs.data.AspectRatioHelper  NONE !com.example.tcs.data.CameraFilter  displayName !com.example.tcs.data.CameraFilter  values !com.example.tcs.data.CameraFilter  aspectRatio #com.example.tcs.data.CameraSettings  captureMode #com.example.tcs.data.CameraSettings  copy #com.example.tcs.data.CameraSettings  exposureCompensation #com.example.tcs.data.CameraSettings  filter #com.example.tcs.data.CameraSettings  	flashMode #com.example.tcs.data.CameraSettings  	gridLines #com.example.tcs.data.CameraSettings  hdr #com.example.tcs.data.CameraSettings  imageQuality #com.example.tcs.data.CameraSettings  iso #com.example.tcs.data.CameraSettings  
lensFacing #com.example.tcs.data.CameraSettings  locationTagging #com.example.tcs.data.CameraSettings  
manualMode #com.example.tcs.data.CameraSettings  mirrorFrontCamera #com.example.tcs.data.CameraSettings  soundEnabled #com.example.tcs.data.CameraSettings  
stabilization #com.example.tcs.data.CameraSettings  
timerDelay #com.example.tcs.data.CameraSettings  videoQuality #com.example.tcs.data.CameraSettings  whiteBalance #com.example.tcs.data.CameraSettings  NIGHT  com.example.tcs.data.CaptureMode  PHOTO  com.example.tcs.data.CaptureMode  PORTRAIT  com.example.tcs.data.CaptureMode  VIDEO  com.example.tcs.data.CaptureMode  to  com.example.tcs.data.CaptureMode  AUTO com.example.tcs.data.FlashMode  ImageCapture com.example.tcs.data.FlashMode  OFF com.example.tcs.data.FlashMode  ON com.example.tcs.data.FlashMode  HIGH !com.example.tcs.data.ImageQuality  displayName !com.example.tcs.data.ImageQuality  values !com.example.tcs.data.ImageQuality  OFF com.example.tcs.data.TimerDelay  displayName com.example.tcs.data.TimerDelay  values com.example.tcs.data.TimerDelay  HD !com.example.tcs.data.VideoQuality  displayName !com.example.tcs.data.VideoQuality  values !com.example.tcs.data.VideoQuality  AUTO !com.example.tcs.data.WhiteBalance  displayName !com.example.tcs.data.WhiteBalance  values !com.example.tcs.data.WhiteBalance  	Alignment com.example.tcs.ui.camera  AndroidView com.example.tcs.ui.camera  Arrangement com.example.tcs.ui.camera  Boolean com.example.tcs.ui.camera  BottomControlBar com.example.tcs.ui.camera  Box com.example.tcs.ui.camera  Button com.example.tcs.ui.camera  	CameraAlt com.example.tcs.ui.camera  CameraPreviewScreen com.example.tcs.ui.camera  CameraState com.example.tcs.ui.camera  CameraViewModel com.example.tcs.ui.camera  
CaptureButton com.example.tcs.ui.camera  CaptureMode com.example.tcs.ui.camera  CaptureModeSelector com.example.tcs.ui.camera  Card com.example.tcs.ui.camera  CardDefaults com.example.tcs.ui.camera  CircleShape com.example.tcs.ui.camera  CircularProgressIndicator com.example.tcs.ui.camera  Color com.example.tcs.ui.camera  Column com.example.tcs.ui.camera  
Composable com.example.tcs.ui.camera  ExperimentalPermissionsApi com.example.tcs.ui.camera  	FlashAuto com.example.tcs.ui.camera  	FlashMode com.example.tcs.ui.camera  FlashOff com.example.tcs.ui.camera  FlashOn com.example.tcs.ui.camera  FlashOption com.example.tcs.ui.camera  FlashOptionsMenu com.example.tcs.ui.camera  FlipCameraAndroid com.example.tcs.ui.camera  Icon com.example.tcs.ui.camera  
IconButton com.example.tcs.ui.camera  Icons com.example.tcs.ui.camera  Int com.example.tcs.ui.camera  LaunchedEffect com.example.tcs.ui.camera  LocalContentColor com.example.tcs.ui.camera  
MaterialTheme com.example.tcs.ui.camera  Modifier com.example.tcs.ui.camera  OptIn com.example.tcs.ui.camera  PermissionRequestScreen com.example.tcs.ui.camera  PermissionUtils com.example.tcs.ui.camera  PhotoLibrary com.example.tcs.ui.camera  PreviewView com.example.tcs.ui.camera  RoundedCornerShape com.example.tcs.ui.camera  Row com.example.tcs.ui.camera  Settings com.example.tcs.ui.camera  Spacer com.example.tcs.ui.camera  String com.example.tcs.ui.camera  Text com.example.tcs.ui.camera  
TopControlBar com.example.tcs.ui.camera  Unit com.example.tcs.ui.camera  Uri com.example.tcs.ui.camera  align com.example.tcs.ui.camera  androidx com.example.tcs.ui.camera  apply com.example.tcs.ui.camera  
background com.example.tcs.ui.camera  
cardColors com.example.tcs.ui.camera  	clickable com.example.tcs.ui.camera  collectAsState com.example.tcs.ui.camera  com com.example.tcs.ui.camera  fillMaxSize com.example.tcs.ui.camera  fillMaxWidth com.example.tcs.ui.camera  forEach com.example.tcs.ui.camera  getCameraPermissions com.example.tcs.ui.camera  getValue com.example.tcs.ui.camera  height com.example.tcs.ui.camera  let com.example.tcs.ui.camera  listOf com.example.tcs.ui.camera  mutableStateOf com.example.tcs.ui.camera  padding com.example.tcs.ui.camera  provideDelegate com.example.tcs.ui.camera  remember com.example.tcs.ui.camera  setValue com.example.tcs.ui.camera  size com.example.tcs.ui.camera  spacedBy com.example.tcs.ui.camera  to com.example.tcs.ui.camera  toList com.example.tcs.ui.camera  weight com.example.tcs.ui.camera  width com.example.tcs.ui.camera  compose "com.example.tcs.ui.camera.androidx  ui *com.example.tcs.ui.camera.androidx.compose  graphics -com.example.tcs.ui.camera.androidx.compose.ui  vector 6com.example.tcs.ui.camera.androidx.compose.ui.graphics  ImageVector =com.example.tcs.ui.camera.androidx.compose.ui.graphics.vector  example com.example.tcs.ui.camera.com  google com.example.tcs.ui.camera.com  tcs %com.example.tcs.ui.camera.com.example  data )com.example.tcs.ui.camera.com.example.tcs  CameraSettings .com.example.tcs.ui.camera.com.example.tcs.data  accompanist $com.example.tcs.ui.camera.com.google  permissions 0com.example.tcs.ui.camera.com.google.accompanist  MultiplePermissionsState <com.example.tcs.ui.camera.com.google.accompanist.permissions  	Alignment com.example.tcs.ui.editor  Arrangement com.example.tcs.ui.editor  	ArrowBack com.example.tcs.ui.editor  
AsyncImage com.example.tcs.ui.editor  Boolean com.example.tcs.ui.editor  Box com.example.tcs.ui.editor  Brightness6 com.example.tcs.ui.editor  BrightnessControl com.example.tcs.ui.editor  Canvas com.example.tcs.ui.editor  Card com.example.tcs.ui.editor  CardDefaults com.example.tcs.ui.editor  CircleShape com.example.tcs.ui.editor  ClosedFloatingPointRange com.example.tcs.ui.editor  Color com.example.tcs.ui.editor  Colorize com.example.tcs.ui.editor  Column com.example.tcs.ui.editor  
Composable com.example.tcs.ui.editor  ContentScale com.example.tcs.ui.editor  Contrast com.example.tcs.ui.editor  ContrastControl com.example.tcs.ui.editor  Crop com.example.tcs.ui.editor  EditTool com.example.tcs.ui.editor  EditToolbar com.example.tcs.ui.editor  ExperimentalMaterial3Api com.example.tcs.ui.editor  
FilterChip com.example.tcs.ui.editor  
FilterControl com.example.tcs.ui.editor  
FilterVintage com.example.tcs.ui.editor  Flip com.example.tcs.ui.editor  FlipCameraAndroid com.example.tcs.ui.editor  Float com.example.tcs.ui.editor  
FontWeight com.example.tcs.ui.editor  Icon com.example.tcs.ui.editor  
IconButton com.example.tcs.ui.editor  Icons com.example.tcs.ui.editor  ImageEditorScreen com.example.tcs.ui.editor  ImageEditorViewModel com.example.tcs.ui.editor  ImageFilter com.example.tcs.ui.editor  LaunchedEffect com.example.tcs.ui.editor  LazyRow com.example.tcs.ui.editor  
MaterialTheme com.example.tcs.ui.editor  Modifier com.example.tcs.ui.editor  OptIn com.example.tcs.ui.editor  ParameterControl com.example.tcs.ui.editor  
RotateControl com.example.tcs.ui.editor  
RotateLeft com.example.tcs.ui.editor  RotateRight com.example.tcs.ui.editor  RoundedCornerShape com.example.tcs.ui.editor  Row com.example.tcs.ui.editor  SaturationControl com.example.tcs.ui.editor  Scaffold com.example.tcs.ui.editor  Slider com.example.tcs.ui.editor  Spacer com.example.tcs.ui.editor  String com.example.tcs.ui.editor  Text com.example.tcs.ui.editor  
TextButton com.example.tcs.ui.editor  
ToolButton com.example.tcs.ui.editor  	TopAppBar com.example.tcs.ui.editor  Unit com.example.tcs.ui.editor  Uri com.example.tcs.ui.editor  androidx com.example.tcs.ui.editor  
asImageBitmap com.example.tcs.ui.editor  
background com.example.tcs.ui.editor  
cardElevation com.example.tcs.ui.editor  collectAsState com.example.tcs.ui.editor  fillMaxSize com.example.tcs.ui.editor  fillMaxWidth com.example.tcs.ui.editor  getValue com.example.tcs.ui.editor  height com.example.tcs.ui.editor  let com.example.tcs.ui.editor  padding com.example.tcs.ui.editor  provideDelegate com.example.tcs.ui.editor  rangeTo com.example.tcs.ui.editor  run com.example.tcs.ui.editor  size com.example.tcs.ui.editor  spacedBy com.example.tcs.ui.editor  width com.example.tcs.ui.editor  
BRIGHTNESS "com.example.tcs.ui.editor.EditTool  Brightness6 "com.example.tcs.ui.editor.EditTool  CONTRAST "com.example.tcs.ui.editor.EditTool  Colorize "com.example.tcs.ui.editor.EditTool  Contrast "com.example.tcs.ui.editor.EditTool  Crop "com.example.tcs.ui.editor.EditTool  FILTER "com.example.tcs.ui.editor.EditTool  
FilterVintage "com.example.tcs.ui.editor.EditTool  Icons "com.example.tcs.ui.editor.EditTool  ROTATE "com.example.tcs.ui.editor.EditTool  RotateRight "com.example.tcs.ui.editor.EditTool  
SATURATION "com.example.tcs.ui.editor.EditTool  displayName "com.example.tcs.ui.editor.EditTool  icon "com.example.tcs.ui.editor.EditTool  values "com.example.tcs.ui.editor.EditTool  COOL %com.example.tcs.ui.editor.ImageFilter  DRAMATIC %com.example.tcs.ui.editor.ImageFilter  	GRAYSCALE %com.example.tcs.ui.editor.ImageFilter  NONE %com.example.tcs.ui.editor.ImageFilter  SEPIA %com.example.tcs.ui.editor.ImageFilter  VINTAGE %com.example.tcs.ui.editor.ImageFilter  VIVID %com.example.tcs.ui.editor.ImageFilter  WARM %com.example.tcs.ui.editor.ImageFilter  displayName %com.example.tcs.ui.editor.ImageFilter  values %com.example.tcs.ui.editor.ImageFilter  compose "com.example.tcs.ui.editor.androidx  ui *com.example.tcs.ui.editor.androidx.compose  graphics -com.example.tcs.ui.editor.androidx.compose.ui  vector 6com.example.tcs.ui.editor.androidx.compose.ui.graphics  ImageVector =com.example.tcs.ui.editor.androidx.compose.ui.graphics.vector  	Alignment com.example.tcs.ui.gallery  Arrangement com.example.tcs.ui.gallery  	ArrowBack com.example.tcs.ui.gallery  
AsyncImage com.example.tcs.ui.gallery  Boolean com.example.tcs.ui.gallery  Box com.example.tcs.ui.gallery  CircularProgressIndicator com.example.tcs.ui.gallery  Color com.example.tcs.ui.gallery  Column com.example.tcs.ui.gallery  
Composable com.example.tcs.ui.gallery  ContentScale com.example.tcs.ui.gallery  EmptyGalleryMessage com.example.tcs.ui.gallery  ExperimentalMaterial3Api com.example.tcs.ui.gallery  
GalleryScreen com.example.tcs.ui.gallery  GalleryViewModel com.example.tcs.ui.gallery  	GridCells com.example.tcs.ui.gallery  Icon com.example.tcs.ui.gallery  
IconButton com.example.tcs.ui.gallery  Icons com.example.tcs.ui.gallery  LaunchedEffect com.example.tcs.ui.gallery  LazyVerticalGrid com.example.tcs.ui.gallery  Long com.example.tcs.ui.gallery  
MaterialTheme com.example.tcs.ui.gallery  	MediaFile com.example.tcs.ui.gallery  MediaThumbnail com.example.tcs.ui.gallery  Modifier com.example.tcs.ui.gallery  OptIn com.example.tcs.ui.gallery  
PaddingValues com.example.tcs.ui.gallery  PhotoLibrary com.example.tcs.ui.gallery  	PlayArrow com.example.tcs.ui.gallery  Refresh com.example.tcs.ui.gallery  RoundedCornerShape com.example.tcs.ui.gallery  Scaffold com.example.tcs.ui.gallery  Spacer com.example.tcs.ui.gallery  String com.example.tcs.ui.gallery  Text com.example.tcs.ui.gallery  	TopAppBar com.example.tcs.ui.gallery  Unit com.example.tcs.ui.gallery  Uri com.example.tcs.ui.gallery  align com.example.tcs.ui.gallery  aspectRatio com.example.tcs.ui.gallery  
background com.example.tcs.ui.gallery  collectAsState com.example.tcs.ui.gallery  fillMaxSize com.example.tcs.ui.gallery  format com.example.tcs.ui.gallery  formatDuration com.example.tcs.ui.gallery  getValue com.example.tcs.ui.gallery  height com.example.tcs.ui.gallery  padding com.example.tcs.ui.gallery  provideDelegate com.example.tcs.ui.gallery  size com.example.tcs.ui.gallery  spacedBy com.example.tcs.ui.gallery  	dateAdded $com.example.tcs.ui.gallery.MediaFile  duration $com.example.tcs.ui.gallery.MediaFile  isVideo $com.example.tcs.ui.gallery.MediaFile  uri $com.example.tcs.ui.gallery.MediaFile  	Alignment com.example.tcs.ui.professional  Arrangement com.example.tcs.ui.professional  Camera com.example.tcs.ui.professional  
CameraEnhance com.example.tcs.ui.professional  CameraViewModel com.example.tcs.ui.professional  Card com.example.tcs.ui.professional  CardDefaults com.example.tcs.ui.professional  CenterFocusStrong com.example.tcs.ui.professional  Close com.example.tcs.ui.professional  Color com.example.tcs.ui.professional  Column com.example.tcs.ui.professional  
Composable com.example.tcs.ui.professional  Exposure com.example.tcs.ui.professional  
FilterChip com.example.tcs.ui.professional  FilterChipDefaults com.example.tcs.ui.professional  
FontWeight com.example.tcs.ui.professional  Icon com.example.tcs.ui.professional  
IconButton com.example.tcs.ui.professional  Icons com.example.tcs.ui.professional  
MaterialTheme com.example.tcs.ui.professional  Modifier com.example.tcs.ui.professional  ProfessionalControl com.example.tcs.ui.professional  ProfessionalModeScreen com.example.tcs.ui.professional  Refresh com.example.tcs.ui.professional  RoundedCornerShape com.example.tcs.ui.professional  Row com.example.tcs.ui.professional  Slider com.example.tcs.ui.professional  SliderDefaults com.example.tcs.ui.professional  Spacer com.example.tcs.ui.professional  String com.example.tcs.ui.professional  Text com.example.tcs.ui.professional  Unit com.example.tcs.ui.professional  WbSunny com.example.tcs.ui.professional  WhiteBalance com.example.tcs.ui.professional  androidx com.example.tcs.ui.professional  
cardColors com.example.tcs.ui.professional  collectAsState com.example.tcs.ui.professional  colors com.example.tcs.ui.professional  fillMaxSize com.example.tcs.ui.professional  fillMaxWidth com.example.tcs.ui.professional  filterChipColors com.example.tcs.ui.professional  forEach com.example.tcs.ui.professional  getValue com.example.tcs.ui.professional  height com.example.tcs.ui.professional  listOf com.example.tcs.ui.professional  padding com.example.tcs.ui.professional  provideDelegate com.example.tcs.ui.professional  rangeTo com.example.tcs.ui.professional  size com.example.tcs.ui.professional  spacedBy com.example.tcs.ui.professional  weight com.example.tcs.ui.professional  width com.example.tcs.ui.professional  compose (com.example.tcs.ui.professional.androidx  ui 0com.example.tcs.ui.professional.androidx.compose  graphics 3com.example.tcs.ui.professional.androidx.compose.ui  vector <com.example.tcs.ui.professional.androidx.compose.ui.graphics  ImageVector Ccom.example.tcs.ui.professional.androidx.compose.ui.graphics.vector  	Alignment com.example.tcs.ui.settings  Arrangement com.example.tcs.ui.settings  	ArrowBack com.example.tcs.ui.settings  
ArrowDropDown com.example.tcs.ui.settings  AspectRatio com.example.tcs.ui.settings  AspectRatioHelper com.example.tcs.ui.settings  Boolean com.example.tcs.ui.settings  Box com.example.tcs.ui.settings  
CameraEnhance com.example.tcs.ui.settings  CameraFilter com.example.tcs.ui.settings  CameraSettingsScreen com.example.tcs.ui.settings  CameraViewModel com.example.tcs.ui.settings  Card com.example.tcs.ui.settings  CardDefaults com.example.tcs.ui.settings  Check com.example.tcs.ui.settings  ClosedFloatingPointRange com.example.tcs.ui.settings  Column com.example.tcs.ui.settings  ColumnScope com.example.tcs.ui.settings  
Composable com.example.tcs.ui.settings  DropdownMenu com.example.tcs.ui.settings  DropdownMenuItem com.example.tcs.ui.settings  ExperimentalMaterial3Api com.example.tcs.ui.settings  Exposure com.example.tcs.ui.settings  
FilterVintage com.example.tcs.ui.settings  FlipCameraAndroid com.example.tcs.ui.settings  Float com.example.tcs.ui.settings  Grid3x3 com.example.tcs.ui.settings  HighQuality com.example.tcs.ui.settings  Icon com.example.tcs.ui.settings  
IconButton com.example.tcs.ui.settings  Icons com.example.tcs.ui.settings  ImageQuality com.example.tcs.ui.settings  ImageVector com.example.tcs.ui.settings  Int com.example.tcs.ui.settings  Iso com.example.tcs.ui.settings  List com.example.tcs.ui.settings  
LocationOn com.example.tcs.ui.settings  
MaterialTheme com.example.tcs.ui.settings  Modifier com.example.tcs.ui.settings  OptIn com.example.tcs.ui.settings  
PaddingValues com.example.tcs.ui.settings  Row com.example.tcs.ui.settings  Scaffold com.example.tcs.ui.settings  SettingsDropdown com.example.tcs.ui.settings  SettingsSection com.example.tcs.ui.settings  SettingsSlider com.example.tcs.ui.settings  SettingsSwitch com.example.tcs.ui.settings  Slider com.example.tcs.ui.settings  Spacer com.example.tcs.ui.settings  String com.example.tcs.ui.settings  Switch com.example.tcs.ui.settings  Text com.example.tcs.ui.settings  
TextButton com.example.tcs.ui.settings  Timer com.example.tcs.ui.settings  
TimerDelay com.example.tcs.ui.settings  	TopAppBar com.example.tcs.ui.settings  Tune com.example.tcs.ui.settings  Unit com.example.tcs.ui.settings  VideoQuality com.example.tcs.ui.settings  
VideoSettings com.example.tcs.ui.settings  VolumeUp com.example.tcs.ui.settings  WbSunny com.example.tcs.ui.settings  WhiteBalance com.example.tcs.ui.settings  
cardElevation com.example.tcs.ui.settings  collectAsState com.example.tcs.ui.settings  fillMaxSize com.example.tcs.ui.settings  fillMaxWidth com.example.tcs.ui.settings  forEachIndexed com.example.tcs.ui.settings  getAllRatios com.example.tcs.ui.settings  getDisplayName com.example.tcs.ui.settings  getValue com.example.tcs.ui.settings  height com.example.tcs.ui.settings  indexOf com.example.tcs.ui.settings  indexOfFirst com.example.tcs.ui.settings  map com.example.tcs.ui.settings  mutableStateOf com.example.tcs.ui.settings  padding com.example.tcs.ui.settings  provideDelegate com.example.tcs.ui.settings  rangeTo com.example.tcs.ui.settings  remember com.example.tcs.ui.settings  setValue com.example.tcs.ui.settings  size com.example.tcs.ui.settings  spacedBy com.example.tcs.ui.settings  weight com.example.tcs.ui.settings  width com.example.tcs.ui.settings  Boolean com.example.tcs.ui.theme  Build com.example.tcs.ui.theme  
Composable com.example.tcs.ui.theme  DarkColorScheme com.example.tcs.ui.theme  
FontFamily com.example.tcs.ui.theme  
FontWeight com.example.tcs.ui.theme  LightColorScheme com.example.tcs.ui.theme  Pink40 com.example.tcs.ui.theme  Pink80 com.example.tcs.ui.theme  Purple40 com.example.tcs.ui.theme  Purple80 com.example.tcs.ui.theme  PurpleGrey40 com.example.tcs.ui.theme  PurpleGrey80 com.example.tcs.ui.theme  TcsTheme com.example.tcs.ui.theme  
Typography com.example.tcs.ui.theme  Unit com.example.tcs.ui.theme  	Alignment com.example.tcs.ui.timelapse  Arrangement com.example.tcs.ui.timelapse  	ArrowBack com.example.tcs.ui.timelapse  Box com.example.tcs.ui.timelapse  Button com.example.tcs.ui.timelapse  ButtonDefaults com.example.tcs.ui.timelapse  Card com.example.tcs.ui.timelapse  CardDefaults com.example.tcs.ui.timelapse  CircleShape com.example.tcs.ui.timelapse  Color com.example.tcs.ui.timelapse  Column com.example.tcs.ui.timelapse  
Composable com.example.tcs.ui.timelapse  
FontWeight com.example.tcs.ui.timelapse  Icon com.example.tcs.ui.timelapse  
IconButton com.example.tcs.ui.timelapse  Icons com.example.tcs.ui.timelapse  Int com.example.tcs.ui.timelapse  LaunchedEffect com.example.tcs.ui.timelapse  Long com.example.tcs.ui.timelapse  
MaterialTheme com.example.tcs.ui.timelapse  Modifier com.example.tcs.ui.timelapse  	PlayArrow com.example.tcs.ui.timelapse  RecordingStatus com.example.tcs.ui.timelapse  Row com.example.tcs.ui.timelapse  SettingItem com.example.tcs.ui.timelapse  
SettingsPanel com.example.tcs.ui.timelapse  Slider com.example.tcs.ui.timelapse  SliderDefaults com.example.tcs.ui.timelapse  Spacer com.example.tcs.ui.timelapse  
StatusItem com.example.tcs.ui.timelapse  Stop com.example.tcs.ui.timelapse  String com.example.tcs.ui.timelapse  Text com.example.tcs.ui.timelapse  TimeLapseScreen com.example.tcs.ui.timelapse  TimeLapseSettings com.example.tcs.ui.timelapse  Unit com.example.tcs.ui.timelapse  
background com.example.tcs.ui.timelapse  buttonColors com.example.tcs.ui.timelapse  
cardColors com.example.tcs.ui.timelapse  colors com.example.tcs.ui.timelapse  delay com.example.tcs.ui.timelapse  fillMaxSize com.example.tcs.ui.timelapse  fillMaxWidth com.example.tcs.ui.timelapse  format com.example.tcs.ui.timelapse  
formatTime com.example.tcs.ui.timelapse  getValue com.example.tcs.ui.timelapse  height com.example.tcs.ui.timelapse  mutableStateOf com.example.tcs.ui.timelapse  padding com.example.tcs.ui.timelapse  
plusAssign com.example.tcs.ui.timelapse  provideDelegate com.example.tcs.ui.timelapse  rangeTo com.example.tcs.ui.timelapse  remember com.example.tcs.ui.timelapse  setValue com.example.tcs.ui.timelapse  size com.example.tcs.ui.timelapse  spacedBy com.example.tcs.ui.timelapse  weight com.example.tcs.ui.timelapse  width com.example.tcs.ui.timelapse  copy .com.example.tcs.ui.timelapse.TimeLapseSettings  durationMinutes .com.example.tcs.ui.timelapse.TimeLapseSettings  intervalSeconds .com.example.tcs.ui.timelapse.TimeLapseSettings  AlertDialog com.example.tcs.ui.viewer  	Alignment com.example.tcs.ui.viewer  Arrangement com.example.tcs.ui.viewer  	ArrowBack com.example.tcs.ui.viewer  
AsyncImage com.example.tcs.ui.viewer  BottomControlBar com.example.tcs.ui.viewer  Box com.example.tcs.ui.viewer  CenterFocusWeak com.example.tcs.ui.viewer  CircleShape com.example.tcs.ui.viewer  Color com.example.tcs.ui.viewer  
Composable com.example.tcs.ui.viewer  ContentScale com.example.tcs.ui.viewer  Context com.example.tcs.ui.viewer  Delete com.example.tcs.ui.viewer  Edit com.example.tcs.ui.viewer  	Exception com.example.tcs.ui.viewer  Icon com.example.tcs.ui.viewer  
IconButton com.example.tcs.ui.viewer  Icons com.example.tcs.ui.viewer  ImageViewerScreen com.example.tcs.ui.viewer  Info com.example.tcs.ui.viewer  Modifier com.example.tcs.ui.viewer  Offset com.example.tcs.ui.viewer  Row com.example.tcs.ui.viewer  Share com.example.tcs.ui.viewer  
ShareUtils com.example.tcs.ui.viewer  Spacer com.example.tcs.ui.viewer  Text com.example.tcs.ui.viewer  
TextButton com.example.tcs.ui.viewer  
TopControlBar com.example.tcs.ui.viewer  Unit com.example.tcs.ui.viewer  Uri com.example.tcs.ui.viewer  align com.example.tcs.ui.viewer  
background com.example.tcs.ui.viewer  	clickable com.example.tcs.ui.viewer  deleteImage com.example.tcs.ui.viewer  fillMaxSize com.example.tcs.ui.viewer  fillMaxWidth com.example.tcs.ui.viewer  getValue com.example.tcs.ui.viewer  
graphicsLayer com.example.tcs.ui.viewer  mutableStateOf com.example.tcs.ui.viewer  padding com.example.tcs.ui.viewer  
plusAssign com.example.tcs.ui.viewer  provideDelegate com.example.tcs.ui.viewer  remember com.example.tcs.ui.viewer  setValue com.example.tcs.ui.viewer  
shareImage com.example.tcs.ui.viewer  timesAssign com.example.tcs.ui.viewer  
transformable com.example.tcs.ui.viewer  width com.example.tcs.ui.viewer  Array com.example.tcs.utils  	ArrayList com.example.tcs.utils  Bitmap com.example.tcs.utils  Boolean com.example.tcs.utils  Build com.example.tcs.utils  
ContentValues com.example.tcs.utils  Context com.example.tcs.utils  
ContextCompat com.example.tcs.utils  CoroutineScope com.example.tcs.utils  Date com.example.tcs.utils  Environment com.example.tcs.utils  ErrorHandler com.example.tcs.utils  	Exception com.example.tcs.utils  File com.example.tcs.utils  FileOutputStream com.example.tcs.utils  FileProvider com.example.tcs.utils  IMAGE_MIME_TYPE com.example.tcs.utils  IOException com.example.tcs.utils  IllegalArgumentException com.example.tcs.utils  IllegalStateException com.example.tcs.utils  ImageCaptureException com.example.tcs.utils  Intent com.example.tcs.utils  List com.example.tcs.utils  Locale com.example.tcs.utils  Log com.example.tcs.utils  Long com.example.tcs.utils  Manifest com.example.tcs.utils  
MediaStore com.example.tcs.utils  
MediaUtils com.example.tcs.utils  PackageManager com.example.tcs.utils  PermissionUtils com.example.tcs.utils  SecurityException com.example.tcs.utils  
ShareUtils com.example.tcs.utils  SimpleDateFormat com.example.tcs.utils  SnackbarHostState com.example.tcs.utils  String com.example.tcs.utils  System com.example.tcs.utils  	Throwable com.example.tcs.utils  Uri com.example.tcs.utils  all com.example.tcs.utils  android com.example.tcs.utils  any com.example.tcs.utils  apply com.example.tcs.utils  contains com.example.tcs.utils  forEach com.example.tcs.utils  format com.example.tcs.utils  getMimeType com.example.tcs.utils  java com.example.tcs.utils  joinToString com.example.tcs.utils  launch com.example.tcs.utils  let com.example.tcs.utils  listOf com.example.tcs.utils  	lowercase com.example.tcs.utils  map com.example.tcs.utils  
mutableListOf com.example.tcs.utils  
startsWith com.example.tcs.utils  substringAfterLast com.example.tcs.utils  toTypedArray com.example.tcs.utils  use com.example.tcs.utils  Log "com.example.tcs.utils.ErrorHandler  TAG "com.example.tcs.utils.ErrorHandler  android "com.example.tcs.utils.ErrorHandler  any "com.example.tcs.utils.ErrorHandler  contains "com.example.tcs.utils.ErrorHandler  joinToString "com.example.tcs.utils.ErrorHandler  launch "com.example.tcs.utils.ErrorHandler  Bitmap  com.example.tcs.utils.MediaUtils  Build  com.example.tcs.utils.MediaUtils  
ContentValues  com.example.tcs.utils.MediaUtils  Date  com.example.tcs.utils.MediaUtils  Environment  com.example.tcs.utils.MediaUtils  FILENAME_FORMAT  com.example.tcs.utils.MediaUtils  File  com.example.tcs.utils.MediaUtils  FileOutputStream  com.example.tcs.utils.MediaUtils  FileProvider  com.example.tcs.utils.MediaUtils  IMAGE_MIME_TYPE  com.example.tcs.utils.MediaUtils  Locale  com.example.tcs.utils.MediaUtils  
MediaStore  com.example.tcs.utils.MediaUtils  SimpleDateFormat  com.example.tcs.utils.MediaUtils  String  com.example.tcs.utils.MediaUtils  System  com.example.tcs.utils.MediaUtils  apply  com.example.tcs.utils.MediaUtils  forEach  com.example.tcs.utils.MediaUtils  format  com.example.tcs.utils.MediaUtils  getMimeType  com.example.tcs.utils.MediaUtils  isImageFile  com.example.tcs.utils.MediaUtils  isVideoFile  com.example.tcs.utils.MediaUtils  let  com.example.tcs.utils.MediaUtils  listOf  com.example.tcs.utils.MediaUtils  	lowercase  com.example.tcs.utils.MediaUtils  saveImageToExternalStorage  com.example.tcs.utils.MediaUtils  saveImageToGallery  com.example.tcs.utils.MediaUtils  saveImageToMediaStore  com.example.tcs.utils.MediaUtils  
startsWith  com.example.tcs.utils.MediaUtils  substringAfterLast  com.example.tcs.utils.MediaUtils  use  com.example.tcs.utils.MediaUtils  Build %com.example.tcs.utils.PermissionUtils  
ContextCompat %com.example.tcs.utils.PermissionUtils  Manifest %com.example.tcs.utils.PermissionUtils  PackageManager %com.example.tcs.utils.PermissionUtils  all %com.example.tcs.utils.PermissionUtils  getCameraPermissions %com.example.tcs.utils.PermissionUtils  
mutableListOf %com.example.tcs.utils.PermissionUtils  toTypedArray %com.example.tcs.utils.PermissionUtils  	ArrayList  com.example.tcs.utils.ShareUtils  FileProvider  com.example.tcs.utils.ShareUtils  Intent  com.example.tcs.utils.ShareUtils  
MediaUtils  com.example.tcs.utils.ShareUtils  apply  com.example.tcs.utils.ShareUtils  getMimeType  com.example.tcs.utils.ShareUtils  let  com.example.tcs.utils.ShareUtils  map  com.example.tcs.utils.ShareUtils  
shareImage  com.example.tcs.utils.ShareUtils  io com.example.tcs.utils.java  net com.example.tcs.utils.java  FileNotFoundException com.example.tcs.utils.java.io  IOException com.example.tcs.utils.java.io  ConnectException com.example.tcs.utils.java.net  SocketTimeoutException com.example.tcs.utils.java.net  UnknownHostException com.example.tcs.utils.java.net  AndroidViewModel com.example.tcs.viewmodel  Application com.example.tcs.viewmodel  Bitmap com.example.tcs.viewmodel  
BitmapFactory com.example.tcs.viewmodel  Boolean com.example.tcs.viewmodel  CameraController com.example.tcs.viewmodel  CameraSettings com.example.tcs.viewmodel  CameraState com.example.tcs.viewmodel  CameraViewModel com.example.tcs.viewmodel  Canvas com.example.tcs.viewmodel  CaptureMode com.example.tcs.viewmodel  ColorMatrix com.example.tcs.viewmodel  ColorMatrixColorFilter com.example.tcs.viewmodel  Context com.example.tcs.viewmodel  Dispatchers com.example.tcs.viewmodel  EditTool com.example.tcs.viewmodel  	Exception com.example.tcs.viewmodel  File com.example.tcs.viewmodel  Float com.example.tcs.viewmodel  GalleryViewModel com.example.tcs.viewmodel  IllegalArgumentException com.example.tcs.viewmodel  ImageEditState com.example.tcs.viewmodel  ImageEditorViewModel com.example.tcs.viewmodel  ImageFilter com.example.tcs.viewmodel  InputStream com.example.tcs.viewmodel  Int com.example.tcs.viewmodel  LifecycleOwner com.example.tcs.viewmodel  List com.example.tcs.viewmodel  Matrix com.example.tcs.viewmodel  	MediaFile com.example.tcs.viewmodel  
MediaStore com.example.tcs.viewmodel  
MediaUtils com.example.tcs.viewmodel  MutableStateFlow com.example.tcs.viewmodel  Paint com.example.tcs.viewmodel  PreviewView com.example.tcs.viewmodel  	StateFlow com.example.tcs.viewmodel  String com.example.tcs.viewmodel  Unit com.example.tcs.viewmodel  Uri com.example.tcs.viewmodel  	ViewModel com.example.tcs.viewmodel  
_editState com.example.tcs.viewmodel  
_isLoading com.example.tcs.viewmodel  _mediaFiles com.example.tcs.viewmodel  applyColorAdjustments com.example.tcs.viewmodel  applyImageFilter com.example.tcs.viewmodel  arrayOf com.example.tcs.viewmodel  asStateFlow com.example.tcs.viewmodel  cameraController com.example.tcs.viewmodel  com com.example.tcs.viewmodel  	emptyList com.example.tcs.viewmodel  	extension com.example.tcs.viewmodel  
flipBitmap com.example.tcs.viewmodel  floatArrayOf com.example.tcs.viewmodel  forEach com.example.tcs.viewmodel  launch com.example.tcs.viewmodel  listOf com.example.tcs.viewmodel  loadBitmapFromUri com.example.tcs.viewmodel  loadImagesAndVideos com.example.tcs.viewmodel  loadMediaFiles com.example.tcs.viewmodel  	lowercase com.example.tcs.viewmodel  
mutableListOf com.example.tcs.viewmodel  originalBitmap com.example.tcs.viewmodel  rotateBitmap com.example.tcs.viewmodel  saveImageToGallery com.example.tcs.viewmodel  sortedByDescending com.example.tcs.viewmodel  use com.example.tcs.viewmodel  withContext com.example.tcs.viewmodel  CameraController )com.example.tcs.viewmodel.CameraViewModel  CameraSettings )com.example.tcs.viewmodel.CameraViewModel  cameraController )com.example.tcs.viewmodel.CameraViewModel  cameraState )com.example.tcs.viewmodel.CameraViewModel  error )com.example.tcs.viewmodel.CameraViewModel  initializeCamera )com.example.tcs.viewmodel.CameraViewModel  invoke )com.example.tcs.viewmodel.CameraViewModel  launch )com.example.tcs.viewmodel.CameraViewModel  resetToDefaults )com.example.tcs.viewmodel.CameraViewModel  settings )com.example.tcs.viewmodel.CameraViewModel  startVideoRecording )com.example.tcs.viewmodel.CameraViewModel  stopVideoRecording )com.example.tcs.viewmodel.CameraViewModel  switchCamera )com.example.tcs.viewmodel.CameraViewModel  	takePhoto )com.example.tcs.viewmodel.CameraViewModel  updateAspectRatio )com.example.tcs.viewmodel.CameraViewModel  updateCaptureMode )com.example.tcs.viewmodel.CameraViewModel  updateExposureCompensation )com.example.tcs.viewmodel.CameraViewModel  updateFilter )com.example.tcs.viewmodel.CameraViewModel  updateFlashMode )com.example.tcs.viewmodel.CameraViewModel  updateGridLines )com.example.tcs.viewmodel.CameraViewModel  	updateHDR )com.example.tcs.viewmodel.CameraViewModel  	updateISO )com.example.tcs.viewmodel.CameraViewModel  updateImageQuality )com.example.tcs.viewmodel.CameraViewModel  updateLocationTagging )com.example.tcs.viewmodel.CameraViewModel  updateManualMode )com.example.tcs.viewmodel.CameraViewModel  updateMirrorFrontCamera )com.example.tcs.viewmodel.CameraViewModel  updateSoundEnabled )com.example.tcs.viewmodel.CameraViewModel  updateStabilization )com.example.tcs.viewmodel.CameraViewModel  updateTimer )com.example.tcs.viewmodel.CameraViewModel  updateVideoQuality )com.example.tcs.viewmodel.CameraViewModel  updateWhiteBalance )com.example.tcs.viewmodel.CameraViewModel  viewModelScope )com.example.tcs.viewmodel.CameraViewModel  Dispatchers *com.example.tcs.viewmodel.GalleryViewModel  File *com.example.tcs.viewmodel.GalleryViewModel  	MediaFile *com.example.tcs.viewmodel.GalleryViewModel  
MediaStore *com.example.tcs.viewmodel.GalleryViewModel  MutableStateFlow *com.example.tcs.viewmodel.GalleryViewModel  Uri *com.example.tcs.viewmodel.GalleryViewModel  
_isLoading *com.example.tcs.viewmodel.GalleryViewModel  _mediaFiles *com.example.tcs.viewmodel.GalleryViewModel  arrayOf *com.example.tcs.viewmodel.GalleryViewModel  asStateFlow *com.example.tcs.viewmodel.GalleryViewModel  	emptyList *com.example.tcs.viewmodel.GalleryViewModel  	extension *com.example.tcs.viewmodel.GalleryViewModel  forEach *com.example.tcs.viewmodel.GalleryViewModel  invoke *com.example.tcs.viewmodel.GalleryViewModel  	isLoading *com.example.tcs.viewmodel.GalleryViewModel  launch *com.example.tcs.viewmodel.GalleryViewModel  listOf *com.example.tcs.viewmodel.GalleryViewModel  loadAppInternalFiles *com.example.tcs.viewmodel.GalleryViewModel  loadImagesAndVideos *com.example.tcs.viewmodel.GalleryViewModel  loadMediaFiles *com.example.tcs.viewmodel.GalleryViewModel  	lowercase *com.example.tcs.viewmodel.GalleryViewModel  
mediaFiles *com.example.tcs.viewmodel.GalleryViewModel  
mutableListOf *com.example.tcs.viewmodel.GalleryViewModel  sortedByDescending *com.example.tcs.viewmodel.GalleryViewModel  use *com.example.tcs.viewmodel.GalleryViewModel  viewModelScope *com.example.tcs.viewmodel.GalleryViewModel  withContext *com.example.tcs.viewmodel.GalleryViewModel  
brightness (com.example.tcs.viewmodel.ImageEditState  contrast (com.example.tcs.viewmodel.ImageEditState  copy (com.example.tcs.viewmodel.ImageEditState  currentTool (com.example.tcs.viewmodel.ImageEditState  editedBitmap (com.example.tcs.viewmodel.ImageEditState  
saturation (com.example.tcs.viewmodel.ImageEditState  selectedFilter (com.example.tcs.viewmodel.ImageEditState  Bitmap .com.example.tcs.viewmodel.ImageEditorViewModel  
BitmapFactory .com.example.tcs.viewmodel.ImageEditorViewModel  Canvas .com.example.tcs.viewmodel.ImageEditorViewModel  ColorMatrix .com.example.tcs.viewmodel.ImageEditorViewModel  ColorMatrixColorFilter .com.example.tcs.viewmodel.ImageEditorViewModel  Dispatchers .com.example.tcs.viewmodel.ImageEditorViewModel  IllegalArgumentException .com.example.tcs.viewmodel.ImageEditorViewModel  ImageEditState .com.example.tcs.viewmodel.ImageEditorViewModel  ImageFilter .com.example.tcs.viewmodel.ImageEditorViewModel  Matrix .com.example.tcs.viewmodel.ImageEditorViewModel  
MediaUtils .com.example.tcs.viewmodel.ImageEditorViewModel  MutableStateFlow .com.example.tcs.viewmodel.ImageEditorViewModel  Paint .com.example.tcs.viewmodel.ImageEditorViewModel  
_editState .com.example.tcs.viewmodel.ImageEditorViewModel  adjustBrightness .com.example.tcs.viewmodel.ImageEditorViewModel  adjustContrast .com.example.tcs.viewmodel.ImageEditorViewModel  adjustSaturation .com.example.tcs.viewmodel.ImageEditorViewModel  applyAllAdjustments .com.example.tcs.viewmodel.ImageEditorViewModel  applyColorAdjustments .com.example.tcs.viewmodel.ImageEditorViewModel  applyCoolFilter .com.example.tcs.viewmodel.ImageEditorViewModel  applyDramaticFilter .com.example.tcs.viewmodel.ImageEditorViewModel  applyFilter .com.example.tcs.viewmodel.ImageEditorViewModel  applyGrayscaleFilter .com.example.tcs.viewmodel.ImageEditorViewModel  applyImageFilter .com.example.tcs.viewmodel.ImageEditorViewModel  applySepiaFilter .com.example.tcs.viewmodel.ImageEditorViewModel  applyVintageFilter .com.example.tcs.viewmodel.ImageEditorViewModel  applyVividFilter .com.example.tcs.viewmodel.ImageEditorViewModel  applyWarmFilter .com.example.tcs.viewmodel.ImageEditorViewModel  asStateFlow .com.example.tcs.viewmodel.ImageEditorViewModel  	editState .com.example.tcs.viewmodel.ImageEditorViewModel  
flipBitmap .com.example.tcs.viewmodel.ImageEditorViewModel  	flipImage .com.example.tcs.viewmodel.ImageEditorViewModel  floatArrayOf .com.example.tcs.viewmodel.ImageEditorViewModel  invoke .com.example.tcs.viewmodel.ImageEditorViewModel  launch .com.example.tcs.viewmodel.ImageEditorViewModel  loadBitmapFromUri .com.example.tcs.viewmodel.ImageEditorViewModel  	loadImage .com.example.tcs.viewmodel.ImageEditorViewModel  originalBitmap .com.example.tcs.viewmodel.ImageEditorViewModel  rotateBitmap .com.example.tcs.viewmodel.ImageEditorViewModel  rotateImage .com.example.tcs.viewmodel.ImageEditorViewModel  saveEditedImage .com.example.tcs.viewmodel.ImageEditorViewModel  saveImageToGallery .com.example.tcs.viewmodel.ImageEditorViewModel  
selectTool .com.example.tcs.viewmodel.ImageEditorViewModel  viewModelScope .com.example.tcs.viewmodel.ImageEditorViewModel  withContext .com.example.tcs.viewmodel.ImageEditorViewModel  example com.example.tcs.viewmodel.com  tcs %com.example.tcs.viewmodel.com.example  data )com.example.tcs.viewmodel.com.example.tcs  CameraFilter .com.example.tcs.viewmodel.com.example.tcs.data  ImageQuality .com.example.tcs.viewmodel.com.example.tcs.data  
TimerDelay .com.example.tcs.viewmodel.com.example.tcs.data  VideoQuality .com.example.tcs.viewmodel.com.example.tcs.data  WhiteBalance .com.example.tcs.viewmodel.com.example.tcs.data  ExperimentalPermissionsApi "com.google.accompanist.permissions  MultiplePermissionsState "com.google.accompanist.permissions   rememberMultiplePermissionsState "com.google.accompanist.permissions  allPermissionsGranted ;com.google.accompanist.permissions.MultiplePermissionsState  launchMultiplePermissionRequest ;com.google.accompanist.permissions.MultiplePermissionsState  ListenableFuture !com.google.common.util.concurrent  get 2com.google.common.util.concurrent.ListenableFuture  File java.io  FileNotFoundException java.io  FileOutputStream java.io  IOException java.io  InputStream java.io  OutputStream java.io  absolutePath java.io.File  createTempFile java.io.File  delete java.io.File  isFile java.io.File  lastModified java.io.File  length java.io.File  	listFiles java.io.File  name java.io.File  use java.io.FileOutputStream  close java.io.InputStream  use java.io.OutputStream  	Exception 	java.lang  IllegalArgumentException 	java.lang  IllegalStateException 	java.lang  SecurityException 	java.lang  message java.lang.Exception  currentTimeMillis java.lang.System  ConnectException java.net  SocketTimeoutException java.net  UnknownHostException java.net  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  	ArrayList 	java.util  Bitmap 	java.util  Boolean 	java.util  Build 	java.util  Camera 	java.util  CameraSelector 	java.util  CameraSettings 	java.util  CameraState 	java.util  
ContentValues 	java.util  Context 	java.util  
ContextCompat 	java.util  Date 	java.util  Environment 	java.util  	Exception 	java.util  ExecutorService 	java.util  	Executors 	java.util  FILENAME_FORMAT 	java.util  File 	java.util  FileOutputOptions 	java.util  FileOutputStream 	java.util  FileProvider 	java.util  IMAGE_MIME_TYPE 	java.util  IOException 	java.util  ImageCapture 	java.util  ImageCaptureException 	java.util  LifecycleOwner 	java.util  Locale 	java.util  Log 	java.util  Long 	java.util  
MediaStore 	java.util  MutableStateFlow 	java.util  Preview 	java.util  PreviewView 	java.util  ProcessCameraProvider 	java.util  Quality 	java.util  QualitySelector 	java.util  Recorder 	java.util  	Recording 	java.util  SimpleDateFormat 	java.util  	StateFlow 	java.util  String 	java.util  System 	java.util  TAG 	java.util  Unit 	java.util  Uri 	java.util  VideoCapture 	java.util  VideoRecordEvent 	java.util  _cameraState 	java.util  also 	java.util  apply 	java.util  asStateFlow 	java.util  forEach 	java.util  format 	java.util  let 	java.util  listOf 	java.util  	lowercase 	java.util  run 	java.util  
startsWith 	java.util  substringAfterLast 	java.util  use 	java.util  OnImageSavedCallback java.util.ImageCapture  OutputFileResults java.util.ImageCapture  US java.util.Locale  Finalize java.util.VideoRecordEvent  Start java.util.VideoRecordEvent  ExecutorService java.util.concurrent  	Executors java.util.concurrent  shutdown $java.util.concurrent.ExecutorService  newSingleThreadExecutor java.util.concurrent.Executors  Array kotlin  Enum kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function3 kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Result kotlin  String kotlin  	Throwable kotlin  also kotlin  apply kotlin  arrayOf kotlin  floatArrayOf kotlin  let kotlin  map kotlin  run kotlin  to kotlin  toList kotlin  use kotlin  all kotlin.Array  forEach kotlin.Array  get kotlin.Array  indexOf kotlin.Array  map kotlin.Array  toList kotlin.Array  not kotlin.Boolean  	compareTo 
kotlin.Double  div 
kotlin.Double  sp 
kotlin.Double  Brightness6 kotlin.Enum  Colorize kotlin.Enum  	Companion kotlin.Enum  Contrast kotlin.Enum  Crop kotlin.Enum  
FilterVintage kotlin.Enum  Icons kotlin.Enum  Int kotlin.Enum  RotateRight kotlin.Enum  String kotlin.Enum  androidx kotlin.Enum  Brightness6 kotlin.Enum.Companion  Colorize kotlin.Enum.Companion  Contrast kotlin.Enum.Companion  Crop kotlin.Enum.Companion  
FilterVintage kotlin.Enum.Companion  Icons kotlin.Enum.Companion  RotateRight kotlin.Enum.Companion  compose kotlin.Enum.androidx  ui kotlin.Enum.androidx.compose  graphics kotlin.Enum.androidx.compose.ui  vector (kotlin.Enum.androidx.compose.ui.graphics  ImageVector /kotlin.Enum.androidx.compose.ui.graphics.vector  div kotlin.Float  plus kotlin.Float  
plusAssign kotlin.Float  rangeTo kotlin.Float  times kotlin.Float  timesAssign kotlin.Float  toInt kotlin.Float  
unaryMinus kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  invoke kotlin.Function2  invoke kotlin.Function3  	compareTo 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  times 
kotlin.Int  to 
kotlin.Int  toFloat 
kotlin.Int  toString 
kotlin.Int  	compareTo kotlin.Long  div kotlin.Long  minus kotlin.Long  plus kotlin.Long  
plusAssign kotlin.Long  rem kotlin.Long  toString kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  first kotlin.Pair  second kotlin.Pair  	Companion 
kotlin.String  contains 
kotlin.String  format 
kotlin.String  let 
kotlin.String  	lowercase 
kotlin.String  substringAfterLast 
kotlin.String  format kotlin.String.Companion  message kotlin.Throwable  List kotlin.collections  MutableList kotlin.collections  all kotlin.collections  any kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  indexOf kotlin.collections  indexOfFirst kotlin.collections  joinToString kotlin.collections  listOf kotlin.collections  map kotlin.collections  
mutableListOf kotlin.collections  
plusAssign kotlin.collections  sortedByDescending kotlin.collections  toList kotlin.collections  toTypedArray kotlin.collections  any kotlin.collections.List  contains kotlin.collections.List  forEachIndexed kotlin.collections.List  get kotlin.collections.List  indexOfFirst kotlin.collections.List  isEmpty kotlin.collections.List  joinToString kotlin.collections.List  map kotlin.collections.List  sortedByDescending kotlin.collections.List  add kotlin.collections.MutableList  addAll kotlin.collections.MutableList  toTypedArray kotlin.collections.MutableList  SuspendFunction1 kotlin.coroutines  	extension 	kotlin.io  
startsWith 	kotlin.io  use 	kotlin.io  ClosedFloatingPointRange 
kotlin.ranges  ClosedRange 
kotlin.ranges  contains 
kotlin.ranges  rangeTo 
kotlin.ranges  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  Sequence kotlin.sequences  all kotlin.sequences  any kotlin.sequences  contains kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  indexOf kotlin.sequences  indexOfFirst kotlin.sequences  joinToString kotlin.sequences  map kotlin.sequences  sortedByDescending kotlin.sequences  toList kotlin.sequences  all kotlin.text  any kotlin.text  contains kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  format kotlin.text  indexOf kotlin.text  indexOfFirst kotlin.text  	lowercase kotlin.text  map kotlin.text  
startsWith kotlin.text  substringAfterLast kotlin.text  toList kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Delay kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  Bitmap !kotlinx.coroutines.CoroutineScope  
BitmapFactory !kotlinx.coroutines.CoroutineScope  Dispatchers !kotlinx.coroutines.CoroutineScope  File !kotlinx.coroutines.CoroutineScope  IllegalArgumentException !kotlinx.coroutines.CoroutineScope  
MediaUtils !kotlinx.coroutines.CoroutineScope  
_editState !kotlinx.coroutines.CoroutineScope  
_isLoading !kotlinx.coroutines.CoroutineScope  _mediaFiles !kotlinx.coroutines.CoroutineScope  applyColorAdjustments !kotlinx.coroutines.CoroutineScope  applyImageFilter !kotlinx.coroutines.CoroutineScope  cameraController !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  	emptyList !kotlinx.coroutines.CoroutineScope  
flipBitmap !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  loadBitmapFromUri !kotlinx.coroutines.CoroutineScope  loadImagesAndVideos !kotlinx.coroutines.CoroutineScope  loadMediaFiles !kotlinx.coroutines.CoroutineScope  originalBitmap !kotlinx.coroutines.CoroutineScope  
plusAssign !kotlinx.coroutines.CoroutineScope  rotateBitmap !kotlinx.coroutines.CoroutineScope  saveImageToGallery !kotlinx.coroutines.CoroutineScope  sortedByDescending !kotlinx.coroutines.CoroutineScope  withContext !kotlinx.coroutines.CoroutineScope  Default kotlinx.coroutines.Dispatchers  IO kotlinx.coroutines.Dispatchers  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  value !kotlinx.coroutines.flow.StateFlow  CircularProgressIndicator android.graphics  SimpleFilter android.graphics  applyBrightnessContrast android.graphics  applySimpleFilter android.graphics  mutableStateOf android.graphics  processAndSaveImage android.graphics  remember android.graphics  rememberCoroutineScope android.graphics  setValue android.graphics  
Parcelable 
android.os  SimpleImageEditorScreen /androidx.compose.animation.AnimatedContentScope  Bitmap "androidx.compose.foundation.layout  
BitmapFactory "androidx.compose.foundation.layout  ColorMatrix "androidx.compose.foundation.layout  ColorMatrixColorFilter "androidx.compose.foundation.layout  Dispatchers "androidx.compose.foundation.layout  InputStream "androidx.compose.foundation.layout  
MediaUtils "androidx.compose.foundation.layout  Paint "androidx.compose.foundation.layout  PermissionRequestScreen "androidx.compose.foundation.layout  SimpleFilter "androidx.compose.foundation.layout  applyBrightnessContrast "androidx.compose.foundation.layout  applySimpleFilter "androidx.compose.foundation.layout  floatArrayOf "androidx.compose.foundation.layout  launch "androidx.compose.foundation.layout  processAndSaveImage "androidx.compose.foundation.layout  rememberCoroutineScope "androidx.compose.foundation.layout  saveImageToGallery "androidx.compose.foundation.layout  PermissionRequestScreen +androidx.compose.foundation.layout.BoxScope  SimpleFilter .androidx.compose.foundation.layout.ColumnScope  
cardElevation .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  launch +androidx.compose.foundation.layout.RowScope  processAndSaveImage +androidx.compose.foundation.layout.RowScope  SimpleFilter .androidx.compose.foundation.lazy.LazyListScope  Bitmap &androidx.compose.material.icons.filled  
BitmapFactory &androidx.compose.material.icons.filled  ColorMatrix &androidx.compose.material.icons.filled  ColorMatrixColorFilter &androidx.compose.material.icons.filled  Dispatchers &androidx.compose.material.icons.filled  InputStream &androidx.compose.material.icons.filled  
MediaUtils &androidx.compose.material.icons.filled  Paint &androidx.compose.material.icons.filled  PermissionRequestScreen &androidx.compose.material.icons.filled  SimpleFilter &androidx.compose.material.icons.filled  applyBrightnessContrast &androidx.compose.material.icons.filled  applySimpleFilter &androidx.compose.material.icons.filled  floatArrayOf &androidx.compose.material.icons.filled  launch &androidx.compose.material.icons.filled  processAndSaveImage &androidx.compose.material.icons.filled  rememberCoroutineScope &androidx.compose.material.icons.filled  saveImageToGallery &androidx.compose.material.icons.filled  Bitmap androidx.compose.material3  
BitmapFactory androidx.compose.material3  ColorMatrix androidx.compose.material3  ColorMatrixColorFilter androidx.compose.material3  Dispatchers androidx.compose.material3  InputStream androidx.compose.material3  
MediaUtils androidx.compose.material3  Paint androidx.compose.material3  PermissionRequestScreen androidx.compose.material3  SimpleFilter androidx.compose.material3  applyBrightnessContrast androidx.compose.material3  applySimpleFilter androidx.compose.material3  floatArrayOf androidx.compose.material3  launch androidx.compose.material3  processAndSaveImage androidx.compose.material3  rememberCoroutineScope androidx.compose.material3  saveImageToGallery androidx.compose.material3  Bitmap androidx.compose.runtime  
BitmapFactory androidx.compose.runtime  ColorMatrix androidx.compose.runtime  ColorMatrixColorFilter androidx.compose.runtime  Dispatchers androidx.compose.runtime  InputStream androidx.compose.runtime  
MediaUtils androidx.compose.runtime  Paint androidx.compose.runtime  PermissionRequestScreen androidx.compose.runtime  SimpleFilter androidx.compose.runtime  SimpleImageEditorScreen androidx.compose.runtime  applyBrightnessContrast androidx.compose.runtime  applySimpleFilter androidx.compose.runtime  floatArrayOf androidx.compose.runtime  launch androidx.compose.runtime  processAndSaveImage androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  saveImageToGallery androidx.compose.runtime  SimpleImageEditorScreen #androidx.navigation.NavGraphBuilder  SimpleImageEditorScreen com.example.tcs  Bitmap com.example.tcs.ui.editor  
BitmapFactory com.example.tcs.ui.editor  CircularProgressIndicator com.example.tcs.ui.editor  ColorMatrix com.example.tcs.ui.editor  ColorMatrixColorFilter com.example.tcs.ui.editor  Context com.example.tcs.ui.editor  Dispatchers com.example.tcs.ui.editor  	Exception com.example.tcs.ui.editor  InputStream com.example.tcs.ui.editor  
MediaUtils com.example.tcs.ui.editor  Paint com.example.tcs.ui.editor  SimpleFilter com.example.tcs.ui.editor  SimpleImageEditorScreen com.example.tcs.ui.editor  applyBrightnessContrast com.example.tcs.ui.editor  applyColorMatrix com.example.tcs.ui.editor  applySimpleFilter com.example.tcs.ui.editor  floatArrayOf com.example.tcs.ui.editor  launch com.example.tcs.ui.editor  mutableStateOf com.example.tcs.ui.editor  processAndSaveImage com.example.tcs.ui.editor  remember com.example.tcs.ui.editor  rememberCoroutineScope com.example.tcs.ui.editor  saveImageToGallery com.example.tcs.ui.editor  setValue com.example.tcs.ui.editor  COOL &com.example.tcs.ui.editor.SimpleFilter  	GRAYSCALE &com.example.tcs.ui.editor.SimpleFilter  NONE &com.example.tcs.ui.editor.SimpleFilter  SEPIA &com.example.tcs.ui.editor.SimpleFilter  WARM &com.example.tcs.ui.editor.SimpleFilter  displayName &com.example.tcs.ui.editor.SimpleFilter  values &com.example.tcs.ui.editor.SimpleFilter  addListener 2com.google.common.util.concurrent.ListenableFuture  Runnable 	java.lang  <SAM-CONSTRUCTOR> java.lang.Runnable  applyBrightnessContrast !kotlinx.coroutines.CoroutineScope  applySimpleFilter !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  processAndSaveImage !kotlinx.coroutines.CoroutineScope  CenterFocusWeak .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  Edit .androidx.compose.foundation.layout.ColumnScope  Info .androidx.compose.foundation.layout.ColumnScope  Share .androidx.compose.foundation.layout.ColumnScope  Card com.example.tcs.ui.viewer  CardDefaults com.example.tcs.ui.viewer  
cardColors com.example.tcs.ui.viewer  size com.example.tcs.ui.viewer                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 