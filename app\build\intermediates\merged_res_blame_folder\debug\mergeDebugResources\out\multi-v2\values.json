{"logs": [{"outputFile": "com.example.tcs.app-mergeDebugResources-67:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9bac6ff2bb2956f26d27e84c234401f2\\transformed\\navigation-common-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "2953,2966,2972,2978,2987", "startColumns": "4,4,4,4,4", "startOffsets": "163529,164168,164412,164659,165022", "endLines": "2965,2971,2977,2980,2991", "endColumns": "24,24,24,24,24", "endOffsets": "164163,164407,164654,164787,165199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\750ad70ca7d2f2aadf98cdad3b4c7def\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "290", "startColumns": "4", "startOffsets": "18332", "endColumns": "42", "endOffsets": "18370"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1f6da9fce089589d440bd174c4a1dc3e\\transformed\\glide-5.0.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}, "to": {"startLines": "255", "startColumns": "4", "startOffsets": "16527", "endColumns": "57", "endOffsets": "16580"}}, {"source": "D:\\tcs\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "34,77,78,79,90,91,94", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "1962,4849,4896,4943,5687,5732,5898", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "1999,4891,4938,4985,5727,5772,5935"}}, {"source": "D:\\tcs\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "42", "endOffsets": "54"}, "to": {"startLines": "330", "startColumns": "4", "startOffsets": "20991", "endColumns": "42", "endOffsets": "21029"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\464fb997f86c74cbb89077e01e66c511\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "267,291", "startColumns": "4,4", "startOffsets": "17131,18375", "endColumns": "41,59", "endOffsets": "17168,18430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ed89daf239d020270328bf0b2917631\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "292", "startColumns": "4", "startOffsets": "18435", "endColumns": "53", "endOffsets": "18484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae455f1a13db310449e207b49d3bacc\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11726,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11781,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,35,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,92,93,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,180,181,182,183,184,185,186,187,188,204,205,206,207,208,209,210,211,247,248,249,250,258,265,266,269,288,296,297,298,299,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,410,421,422,423,424,425,426,434,435,439,443,447,452,458,465,469,473,478,482,486,490,494,498,502,508,512,518,522,528,532,537,541,544,548,554,558,564,568,574,577,581,585,589,593,597,598,599,600,603,606,609,612,616,617,618,619,620,623,625,627,629,634,635,639,645,649,650,652,663,664,668,674,678,679,680,684,711,715,716,720,748,918,944,1115,1141,1172,1180,1186,1200,1222,1227,1232,1242,1251,1260,1264,1271,1279,1286,1287,1296,1299,1302,1306,1310,1314,1317,1318,1323,1328,1338,1343,1350,1356,1357,1360,1364,1369,1371,1373,1376,1379,1381,1385,1388,1395,1398,1401,1405,1407,1411,1413,1415,1417,1421,1429,1437,1449,1455,1464,1467,1478,1481,1482,1487,1488,1516,1585,1655,1656,1666,1675,1676,1678,1682,1685,1688,1691,1694,1697,1700,1703,1707,1710,1713,1716,1720,1723,1727,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1753,1755,1756,1757,1758,1759,1760,1761,1762,1764,1765,1767,1768,1770,1772,1773,1775,1776,1777,1778,1779,1780,1782,1783,1784,1785,1786,1798,1800,1802,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1818,1819,1820,1821,1822,1823,1825,1829,1834,1835,1836,1837,1838,1839,1843,1844,1845,1846,1848,1850,1852,1854,1856,1857,1858,1859,1861,1863,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1879,1880,1881,1882,1884,1886,1887,1889,1890,1892,1894,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1909,1910,1911,1912,1914,1915,1916,1917,1918,1920,1922,1924,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1943,2018,2021,2024,2027,2041,2054,2096,2125,2152,2161,2223,2582,2602,2630,2752,2776,2782,2788,2809,2933,2992,2998,3006,3012,3047,3079,3145,3165,3220,3232,3258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,402,818,859,914,973,1035,1116,1177,1252,1328,1405,1643,1728,1810,1886,2004,2081,2159,2265,2371,2450,2530,2587,2776,2850,2925,2990,3056,3116,3177,3249,3322,3389,3457,3516,3575,3634,3693,3752,3806,3860,3913,3967,4021,4075,4261,4335,4414,4487,4561,4632,4704,4776,4990,5047,5105,5178,5252,5326,5401,5473,5546,5616,5777,5837,5940,6009,6078,6148,6222,6298,6362,6439,6515,6592,6657,6726,6803,6878,6947,7015,7092,7158,7219,7316,7381,7450,7549,7620,7679,7737,7794,7853,7917,7988,8060,8132,8204,8276,8343,8411,8479,8538,8601,8665,8755,8846,8906,8972,9039,9105,9175,9239,9292,9359,9420,9487,9600,9658,9721,9786,9851,9926,9999,10071,10120,10181,10242,10303,10365,10429,10493,10557,10622,10685,10745,10806,10872,10931,10991,11053,11124,11184,11740,11826,11913,12003,12090,12178,12260,12343,12433,13502,13554,13612,13657,13723,13787,13844,13901,16078,16135,16183,16232,16702,17035,17082,17238,18257,18656,18720,18782,18842,19042,19116,19186,19264,19318,19388,19473,19521,19567,19628,19691,19757,19821,19892,19955,20020,20084,20145,20206,20258,20331,20405,20474,20549,20623,20697,20838,26662,27244,27322,27412,27500,27596,27686,28268,28357,28604,28885,29137,29422,29815,30292,30514,30736,31012,31239,31469,31699,31929,32159,32386,32805,33031,33456,33686,34114,34333,34616,34824,34955,35182,35608,35833,36260,36481,36906,37026,37302,37603,37927,38218,38532,38669,38800,38905,39147,39314,39518,39726,39997,40109,40221,40326,40443,40657,40803,40943,41029,41377,41465,41711,42129,42378,42460,42558,43150,43250,43502,43926,44181,44275,44364,44601,46625,46867,46969,47222,49378,59910,61426,72057,73585,75342,75968,76388,77449,78714,78970,79206,79753,80247,80852,81050,81630,82194,82569,82687,83225,83382,83578,83851,84107,84277,84418,84482,84847,85214,85890,86154,86492,86845,86939,87125,87431,87693,87818,87945,88184,88395,88514,88707,88884,89339,89520,89642,89901,90014,90201,90303,90410,90539,90814,91322,91818,92695,92989,93559,93708,94440,94612,94696,95032,95124,96688,101934,107323,107385,107963,108547,108638,108751,108980,109140,109292,109463,109629,109798,109965,110128,110371,110541,110714,110885,111159,111358,111563,111893,111977,112073,112169,112267,112367,112469,112571,112673,112775,112877,112977,113073,113185,113314,113437,113568,113699,113797,113911,114005,114145,114279,114375,114487,114587,114703,114799,114911,115011,115151,115287,115451,115581,115739,115889,116030,116174,116309,116421,116571,116699,116827,116963,117095,117225,117355,117467,118365,118511,118655,118793,118859,118949,119025,119129,119219,119321,119429,119537,119637,119717,119809,119907,120017,120095,120201,120293,120397,120507,120629,120792,121029,121109,121209,121299,121409,121499,121740,121834,121940,122032,122132,122244,122358,122474,122590,122684,122798,122910,123012,123132,123254,123336,123440,123560,123686,123784,123878,123966,124078,124194,124316,124428,124603,124719,124805,124897,125009,125133,125200,125326,125394,125522,125666,125794,125863,125958,126073,126186,126285,126394,126505,126616,126717,126822,126922,127052,127143,127266,127360,127472,127558,127662,127758,127846,127964,128068,128172,128298,128386,128494,128594,128684,128794,128878,128980,129064,129118,129182,129288,129374,129484,129568,129827,132443,132561,132676,132756,133117,133654,135058,136402,137763,138151,140926,150830,151469,152826,157053,157804,158066,158266,158645,162923,165204,165433,165727,165942,167025,167875,170901,171645,173776,174116,175427", "endLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,35,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,92,93,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,180,181,182,183,184,185,186,187,188,204,205,206,207,208,209,210,211,247,248,249,250,258,265,266,269,288,296,297,298,299,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,410,421,422,423,424,425,433,434,438,442,446,451,457,464,468,472,477,481,485,489,493,497,501,507,511,517,521,527,531,536,540,543,547,553,557,563,567,573,576,580,584,588,592,596,597,598,599,602,605,608,611,615,616,617,618,619,622,624,626,628,633,634,638,644,648,649,651,662,663,667,673,677,678,679,683,710,714,715,719,747,917,943,1114,1140,1171,1179,1185,1199,1221,1226,1231,1241,1250,1259,1263,1270,1278,1285,1286,1295,1298,1301,1305,1309,1313,1316,1317,1322,1327,1337,1342,1349,1355,1356,1359,1363,1368,1370,1372,1375,1378,1380,1384,1387,1394,1397,1400,1404,1406,1410,1412,1414,1416,1420,1428,1436,1448,1454,1463,1466,1477,1480,1481,1486,1487,1492,1584,1654,1655,1665,1674,1675,1677,1681,1684,1687,1690,1693,1696,1699,1702,1706,1709,1712,1715,1719,1722,1726,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1752,1754,1755,1756,1757,1758,1759,1760,1761,1763,1764,1766,1767,1769,1771,1772,1774,1775,1776,1777,1778,1779,1781,1782,1783,1784,1785,1786,1799,1801,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1817,1818,1819,1820,1821,1822,1824,1828,1832,1834,1835,1836,1837,1838,1842,1843,1844,1845,1847,1849,1851,1853,1855,1856,1857,1858,1860,1862,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1878,1879,1880,1881,1883,1885,1886,1888,1889,1891,1893,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1908,1909,1910,1911,1913,1914,1915,1916,1917,1919,1921,1923,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,2017,2020,2023,2026,2040,2046,2063,2124,2151,2160,2222,2581,2585,2629,2647,2775,2781,2787,2808,2932,2952,2997,3001,3011,3046,3058,3144,3164,3219,3231,3257,3264", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,446,854,909,968,1030,1111,1172,1247,1323,1400,1478,1723,1805,1881,1957,2076,2154,2260,2366,2445,2525,2582,2640,2845,2920,2985,3051,3111,3172,3244,3317,3384,3452,3511,3570,3629,3688,3747,3801,3855,3908,3962,4016,4070,4124,4330,4409,4482,4556,4627,4699,4771,4844,5042,5100,5173,5247,5321,5396,5468,5541,5611,5682,5832,5893,6004,6073,6143,6217,6293,6357,6434,6510,6587,6652,6721,6798,6873,6942,7010,7087,7153,7214,7311,7376,7445,7544,7615,7674,7732,7789,7848,7912,7983,8055,8127,8199,8271,8338,8406,8474,8533,8596,8660,8750,8841,8901,8967,9034,9100,9170,9234,9287,9354,9415,9482,9595,9653,9716,9781,9846,9921,9994,10066,10115,10176,10237,10298,10360,10424,10488,10552,10617,10680,10740,10801,10867,10926,10986,11048,11119,11179,11247,11821,11908,11998,12085,12173,12255,12338,12428,12519,13549,13607,13652,13718,13782,13839,13896,13950,16130,16178,16227,16278,16731,17077,17126,17279,18284,18715,18777,18837,18894,19111,19181,19259,19313,19383,19468,19516,19562,19623,19686,19752,19816,19887,19950,20015,20079,20140,20201,20253,20326,20400,20469,20544,20618,20692,20833,20903,26710,27317,27407,27495,27591,27681,28263,28352,28599,28880,29132,29417,29810,30287,30509,30731,31007,31234,31464,31694,31924,32154,32381,32800,33026,33451,33681,34109,34328,34611,34819,34950,35177,35603,35828,36255,36476,36901,37021,37297,37598,37922,38213,38527,38664,38795,38900,39142,39309,39513,39721,39992,40104,40216,40321,40438,40652,40798,40938,41024,41372,41460,41706,42124,42373,42455,42553,43145,43245,43497,43921,44176,44270,44359,44596,46620,46862,46964,47217,49373,59905,61421,72052,73580,75337,75963,76383,77444,78709,78965,79201,79748,80242,80847,81045,81625,82189,82564,82682,83220,83377,83573,83846,84102,84272,84413,84477,84842,85209,85885,86149,86487,86840,86934,87120,87426,87688,87813,87940,88179,88390,88509,88702,88879,89334,89515,89637,89896,90009,90196,90298,90405,90534,90809,91317,91813,92690,92984,93554,93703,94435,94607,94691,95027,95119,95397,101929,107318,107380,107958,108542,108633,108746,108975,109135,109287,109458,109624,109793,109960,110123,110366,110536,110709,110880,111154,111353,111558,111888,111972,112068,112164,112262,112362,112464,112566,112668,112770,112872,112972,113068,113180,113309,113432,113563,113694,113792,113906,114000,114140,114274,114370,114482,114582,114698,114794,114906,115006,115146,115282,115446,115576,115734,115884,116025,116169,116304,116416,116566,116694,116822,116958,117090,117220,117350,117462,117602,118506,118650,118788,118854,118944,119020,119124,119214,119316,119424,119532,119632,119712,119804,119902,120012,120090,120196,120288,120392,120502,120624,120787,120944,121104,121204,121294,121404,121494,121735,121829,121935,122027,122127,122239,122353,122469,122585,122679,122793,122905,123007,123127,123249,123331,123435,123555,123681,123779,123873,123961,124073,124189,124311,124423,124598,124714,124800,124892,125004,125128,125195,125321,125389,125517,125661,125789,125858,125953,126068,126181,126280,126389,126500,126611,126712,126817,126917,127047,127138,127261,127355,127467,127553,127657,127753,127841,127959,128063,128167,128293,128381,128489,128589,128679,128789,128873,128975,129059,129113,129177,129283,129369,129479,129563,129683,132438,132556,132671,132751,133112,133345,134166,136397,137758,138146,140921,150825,150960,152821,153393,157799,158061,158261,158640,162918,163524,165428,165579,165937,167020,167332,170896,171640,173771,174111,175422,175625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3d50080025ffa62904d840df8c6aa998\\transformed\\fragment-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "254,268,294,2704,2709", "startColumns": "4,4,4,4,4", "startOffsets": "16470,17173,18539,155885,156055", "endLines": "254,268,294,2708,2712", "endColumns": "56,64,63,24,24", "endOffsets": "16522,17233,18598,156050,156199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c19c93bf4b3ae41606569ce25471910a\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "260,264", "startColumns": "4,4", "startOffsets": "16791,16968", "endColumns": "53,66", "endOffsets": "16840,17030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\54c8e97fceebf05a01c2f3ccec507e1e\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "293", "startColumns": "4", "startOffsets": "18489", "endColumns": "49", "endOffsets": "18534"}}, {"source": "D:\\tcs\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "80", "endOffsets": "132"}, "to": {"startLines": "1833", "startColumns": "4", "startOffsets": "120949", "endColumns": "79", "endOffsets": "121024"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fcdb86bfab48733c738bc96cd27f5ed\\transformed\\appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2064,2080,2086,3059,3075", "startColumns": "4,4,4,4,4", "startOffsets": "134171,134596,134774,167337,167748", "endLines": "2079,2085,2095,3074,3078", "endColumns": "24,24,24,24,24", "endOffsets": "134591,134769,135053,167743,167870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f5e89f9b119dd4ecc3de589db06b83c8\\transformed\\camera-view-1.3.4\\res\\values\\values.xml", "from": {"startLines": "2,6,14", "startColumns": "4,4,4", "startOffsets": "55,207,514", "endLines": "5,13,17", "endColumns": "11,11,24", "endOffsets": "202,509,652"}, "to": {"startLines": "4,10,3002", "startColumns": "4,4,4", "startOffsets": "250,511,165584", "endLines": "7,17,3005", "endColumns": "11,11,24", "endOffsets": "397,813,165722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\59782c46e92902f9e632069eb2572923\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "329", "startColumns": "4", "startOffsets": "20908", "endColumns": "82", "endOffsets": "20986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d0e5df3da8374bed71d9993cb807cb2d\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "9,28,29,43,44,67,68,173,174,175,176,177,178,179,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,261,262,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,301,331,332,333,334,335,336,337,415,1787,1788,1792,1793,1797,1941,1942,2586,2592,2648,2683,2713,2746", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "451,1483,1555,2645,2710,4129,4198,11252,11322,11390,11462,11532,11593,11667,12524,12585,12646,12708,12772,12834,12895,12963,13063,13123,13189,13262,13331,13388,13440,13955,14027,14103,14168,14227,14286,14346,14406,14466,14526,14586,14646,14706,14766,14826,14886,14945,15005,15065,15125,15185,15245,15305,15365,15425,15485,15545,15604,15664,15724,15783,15842,15901,15960,16019,16845,16880,17284,17339,17402,17457,17515,17571,17629,17690,17753,17810,17861,17919,17969,18030,18087,18153,18187,18222,18972,21034,21101,21173,21242,21311,21385,21457,26889,117607,117724,117925,118035,118236,129688,129760,150965,151168,153398,155204,156204,156886", "endLines": "9,28,29,43,44,67,68,173,174,175,176,177,178,179,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,261,262,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,301,331,332,333,334,335,336,337,415,1787,1791,1792,1796,1797,1941,1942,2591,2601,2682,2703,2745,2751", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "506,1550,1638,2705,2771,4193,4256,11317,11385,11457,11527,11588,11662,11735,12580,12641,12703,12767,12829,12890,12958,13058,13118,13184,13257,13326,13383,13435,13497,14022,14098,14163,14222,14281,14341,14401,14461,14521,14581,14641,14701,14761,14821,14881,14940,15000,15060,15120,15180,15240,15300,15360,15420,15480,15540,15599,15659,15719,15778,15837,15896,15955,16014,16073,16875,16910,17334,17397,17452,17510,17566,17624,17685,17748,17805,17856,17914,17964,18025,18082,18148,18182,18217,18252,19037,21096,21168,21237,21306,21380,21452,21540,26955,117719,117920,118030,118231,118360,129755,129822,151163,151464,155199,155880,156881,157048"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\eb5978be55e4593326b90344069d1b2d\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "251,252,253,257,259,295,338,339,340,341,342,343,344,406,407,408,409,411,412,413,414,416,417,418,1493,1509,1512", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16283,16357,16415,16651,16736,18603,21545,21610,21664,21730,21831,21889,21941,26442,26504,26558,26608,26715,26761,26807,26849,26960,27007,27043,95402,96382,96493", "endLines": "251,252,253,257,259,295,338,339,340,341,342,343,344,406,407,408,409,411,412,413,414,416,417,418,1495,1511,1515", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "16352,16410,16465,16697,16786,18651,21605,21659,21725,21826,21884,21936,21996,26499,26553,26603,26657,26756,26802,26844,26884,27002,27038,27128,95509,96488,96683"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3109e90bbcb133a6123d6a171e482e61\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "419,420", "startColumns": "4,4", "startOffsets": "27133,27189", "endColumns": "55,54", "endOffsets": "27184,27239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5f16fe121e751bf9655a32005d05688f\\transformed\\navigation-runtime-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "263,2047,2981,2984", "startColumns": "4,4,4,4", "startOffsets": "16915,133350,164792,164907", "endLines": "263,2053,2983,2986", "endColumns": "52,24,24,24", "endOffsets": "16963,133649,164902,165017"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e754f461ad8f7a511fa7fc6c4385c693\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "300,345,346,347,348,349,350,351,352,353,354,357,358,359,360,361,362,363,364,365,366,367,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,1496,1506", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18899,22001,22089,22175,22256,22340,22409,22474,22557,22663,22749,22869,22923,22992,23053,23122,23211,23306,23380,23477,23570,23668,23817,23908,23996,24092,24190,24254,24322,24409,24503,24570,24642,24714,24815,24924,25000,25069,25117,25183,25247,25321,25378,25435,25507,25557,25611,25682,25753,25823,25892,25950,26026,26097,26171,26257,26307,26377,95514,96229", "endLines": "300,345,346,347,348,349,350,351,352,353,356,357,358,359,360,361,362,363,364,365,366,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,1505,1508", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "18967,22084,22170,22251,22335,22404,22469,22552,22658,22744,22864,22918,22987,23048,23117,23206,23301,23375,23472,23565,23663,23812,23903,23991,24087,24185,24249,24317,24404,24498,24565,24637,24709,24810,24919,24995,25064,25112,25178,25242,25316,25373,25430,25502,25552,25606,25677,25748,25818,25887,25945,26021,26092,26166,26252,26302,26372,26437,96224,96377"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6f9fffeca9b4e75d900913b18ac2df78\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "289", "startColumns": "4", "startOffsets": "18289", "endColumns": "42", "endOffsets": "18327"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6e4d5a7b637c696b107f8ca7a1e781c7\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "256", "startColumns": "4", "startOffsets": "16585", "endColumns": "65", "endOffsets": "16646"}}]}]}