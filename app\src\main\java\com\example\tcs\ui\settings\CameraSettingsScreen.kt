package com.example.tcs.ui.settings

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.tcs.data.*
import com.example.tcs.viewmodel.CameraViewModel

/**
 * 相机设置界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CameraSettingsScreen(
    onNavigateBack: () -> Unit,
    viewModel: CameraViewModel = viewModel()
) {
    val settings by viewModel.settings.collectAsState()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("相机设置") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    TextButton(onClick = { viewModel.resetToDefaults() }) {
                        Text("重置")
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 基础设置
            item {
                SettingsSection(title = "基础设置") {
                    // 宽高比设置
                    SettingsDropdown(
                        title = "宽高比",
                        subtitle = AspectRatioHelper.getDisplayName(settings.aspectRatio),
                        icon = Icons.Default.AspectRatio,
                        options = AspectRatioHelper.getAllRatios().map { it.second },
                        selectedIndex = AspectRatioHelper.getAllRatios().indexOfFirst { it.first == settings.aspectRatio },
                        onSelectionChanged = { index ->
                            val ratio = AspectRatioHelper.getAllRatios()[index].first
                            viewModel.updateAspectRatio(ratio)
                        }
                    )
                    
                    // 图像质量
                    SettingsDropdown(
                        title = "图像质量",
                        subtitle = settings.imageQuality.displayName,
                        icon = Icons.Default.HighQuality,
                        options = ImageQuality.values().map { it.displayName },
                        selectedIndex = ImageQuality.values().indexOf(settings.imageQuality),
                        onSelectionChanged = { index ->
                            viewModel.updateImageQuality(ImageQuality.values()[index])
                        }
                    )
                    
                    // 视频质量
                    SettingsDropdown(
                        title = "视频质量",
                        subtitle = settings.videoQuality.displayName,
                        icon = Icons.Default.VideoSettings,
                        options = VideoQuality.values().map { it.displayName },
                        selectedIndex = VideoQuality.values().indexOf(settings.videoQuality),
                        onSelectionChanged = { index ->
                            viewModel.updateVideoQuality(VideoQuality.values()[index])
                        }
                    )
                }
            }
            
            // 拍摄辅助
            item {
                SettingsSection(title = "拍摄辅助") {
                    // 网格线
                    SettingsSwitch(
                        title = "网格线",
                        subtitle = "显示九宫格辅助线",
                        icon = Icons.Default.Grid3x3,
                        checked = settings.gridLines,
                        onCheckedChange = { viewModel.updateGridLines(it) }
                    )
                    
                    // 定时器
                    SettingsDropdown(
                        title = "定时器",
                        subtitle = settings.timerDelay.displayName,
                        icon = Icons.Default.Timer,
                        options = TimerDelay.values().map { it.displayName },
                        selectedIndex = TimerDelay.values().indexOf(settings.timerDelay),
                        onSelectionChanged = { index ->
                            viewModel.updateTimer(TimerDelay.values()[index])
                        }
                    )
                    
                    // HDR
                    SettingsSwitch(
                        title = "HDR",
                        subtitle = "高动态范围成像",
                        icon = Icons.Default.Hdr,
                        checked = settings.hdr,
                        onCheckedChange = { viewModel.updateHDR(it) }
                    )
                    
                    // 防抖
                    SettingsSwitch(
                        title = "防抖",
                        subtitle = "图像稳定化",
                        icon = Icons.Default.CameraEnhance,
                        checked = settings.stabilization,
                        onCheckedChange = { viewModel.updateStabilization(it) }
                    )
                }
            }
            
            // 滤镜设置
            item {
                SettingsSection(title = "滤镜") {
                    SettingsDropdown(
                        title = "相机滤镜",
                        subtitle = settings.filter.displayName,
                        icon = Icons.Default.FilterVintage,
                        options = CameraFilter.values().map { it.displayName },
                        selectedIndex = CameraFilter.values().indexOf(settings.filter),
                        onSelectionChanged = { index ->
                            viewModel.updateFilter(CameraFilter.values()[index])
                        }
                    )
                }
            }
            
            // 专业模式
            item {
                SettingsSection(title = "专业模式") {
                    // 手动模式开关
                    SettingsSwitch(
                        title = "专业模式",
                        subtitle = "手动控制相机参数",
                        icon = Icons.Default.Tune,
                        checked = settings.manualMode,
                        onCheckedChange = { viewModel.updateManualMode(it) }
                    )
                    
                    if (settings.manualMode) {
                        // ISO设置
                        SettingsSlider(
                            title = "ISO",
                            subtitle = "当前值: ${settings.iso}",
                            icon = Icons.Default.Iso,
                            value = settings.iso.toFloat(),
                            valueRange = 100f..3200f,
                            steps = 31,
                            onValueChange = { viewModel.updateISO(it.toInt()) }
                        )
                        
                        // 曝光补偿
                        SettingsSlider(
                            title = "曝光补偿",
                            subtitle = "当前值: ${if (settings.exposureCompensation >= 0) "+" else ""}${settings.exposureCompensation}",
                            icon = Icons.Default.Exposure,
                            value = settings.exposureCompensation.toFloat(),
                            valueRange = -6f..6f,
                            steps = 12,
                            onValueChange = { viewModel.updateExposureCompensation(it.toInt()) }
                        )
                        
                        // 白平衡
                        SettingsDropdown(
                            title = "白平衡",
                            subtitle = settings.whiteBalance.displayName,
                            icon = Icons.Default.WbSunny,
                            options = WhiteBalance.values().map { it.displayName },
                            selectedIndex = WhiteBalance.values().indexOf(settings.whiteBalance),
                            onSelectionChanged = { index ->
                                viewModel.updateWhiteBalance(WhiteBalance.values()[index])
                            }
                        )
                    }
                }
            }
            
            // 其他设置
            item {
                SettingsSection(title = "其他设置") {
                    // 拍照声音
                    SettingsSwitch(
                        title = "拍照声音",
                        subtitle = "拍照时播放快门声音",
                        icon = Icons.Default.VolumeUp,
                        checked = settings.soundEnabled,
                        onCheckedChange = { viewModel.updateSoundEnabled(it) }
                    )
                    
                    // 位置标记
                    SettingsSwitch(
                        title = "位置标记",
                        subtitle = "在照片中保存位置信息",
                        icon = Icons.Default.LocationOn,
                        checked = settings.locationTagging,
                        onCheckedChange = { viewModel.updateLocationTagging(it) }
                    )
                    
                    // 前置相机镜像
                    SettingsSwitch(
                        title = "前置相机镜像",
                        subtitle = "前置相机拍照时镜像翻转",
                        icon = Icons.Default.FlipCameraAndroid,
                        checked = settings.mirrorFrontCamera,
                        onCheckedChange = { viewModel.updateMirrorFrontCamera(it) }
                    )
                }
            }
        }
    }
}

/**
 * 设置分组
 */
@Composable
private fun SettingsSection(
    title: String,
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            content()
        }
    }
}

/**
 * 开关设置项
 */
@Composable
private fun SettingsSwitch(
    title: String,
    subtitle: String,
    icon: ImageVector,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(24.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.width(16.dp))

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge
            )
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange
        )
    }
}

/**
 * 下拉选择设置项
 */
@Composable
private fun SettingsDropdown(
    title: String,
    subtitle: String,
    icon: ImageVector,
    options: List<String>,
    selectedIndex: Int,
    onSelectionChanged: (Int) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(24.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.width(16.dp))

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge
            )
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        Box {
            TextButton(onClick = { expanded = true }) {
                Text("选择")
                Icon(
                    imageVector = Icons.Default.ArrowDropDown,
                    contentDescription = null
                )
            }

            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                options.forEachIndexed { index, option ->
                    DropdownMenuItem(
                        text = { Text(option) },
                        onClick = {
                            onSelectionChanged(index)
                            expanded = false
                        },
                        leadingIcon = if (index == selectedIndex) {
                            { Icon(Icons.Default.Check, contentDescription = null) }
                        } else null
                    )
                }
            }
        }
    }
}

/**
 * 滑块设置项
 */
@Composable
private fun SettingsSlider(
    title: String,
    subtitle: String,
    icon: ImageVector,
    value: Float,
    valueRange: ClosedFloatingPointRange<Float>,
    steps: Int,
    onValueChange: (Float) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier = Modifier.size(24.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Spacer(modifier = Modifier.width(16.dp))

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyLarge
                )
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        Slider(
            value = value,
            onValueChange = onValueChange,
            valueRange = valueRange,
            steps = steps,
            modifier = Modifier.padding(start = 40.dp)
        )
    }
}
