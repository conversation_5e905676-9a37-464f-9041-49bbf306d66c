package com.example.tcs.ui.professional

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.tcs.data.WhiteBalance
import com.example.tcs.viewmodel.CameraViewModel

/**
 * 专业模式界面
 */
@Composable
fun ProfessionalModeScreen(
    onNavigateBack: () -> Unit,
    viewModel: CameraViewModel = viewModel()
) {
    val settings by viewModel.settings.collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.8f))
            .padding(16.dp)
    ) {
        // 标题栏
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "关闭",
                    tint = Color.White
                )
            }
            
            Text(
                text = "专业模式",
                style = MaterialTheme.typography.titleLarge,
                color = Color.White,
                fontWeight = FontWeight.Bold
            )
            
            IconButton(onClick = { viewModel.resetToDefaults() }) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "重置",
                    tint = Color.White
                )
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 控制面板
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.6f)
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {
                // ISO控制
                ProfessionalControl(
                    title = "ISO",
                    value = settings.iso.toString(),
                    icon = Icons.Default.Iso,
                    onValueChange = { /* TODO: 实现ISO调整 */ }
                ) {
                    Slider(
                        value = settings.iso.toFloat(),
                        onValueChange = { viewModel.updateISO(it.toInt()) },
                        valueRange = 100f..3200f,
                        steps = 31,
                        colors = SliderDefaults.colors(
                            thumbColor = Color.White,
                            activeTrackColor = Color.White,
                            inactiveTrackColor = Color.White.copy(alpha = 0.3f)
                        )
                    )
                }
                
                // 快门速度控制
                ProfessionalControl(
                    title = "快门",
                    value = "1/60s",
                    icon = Icons.Default.ShutterSpeed,
                    onValueChange = { /* TODO: 实现快门速度调整 */ }
                ) {
                    Slider(
                        value = 0.5f,
                        onValueChange = { /* TODO */ },
                        valueRange = 0f..1f,
                        colors = SliderDefaults.colors(
                            thumbColor = Color.White,
                            activeTrackColor = Color.White,
                            inactiveTrackColor = Color.White.copy(alpha = 0.3f)
                        )
                    )
                }
                
                // 曝光补偿
                ProfessionalControl(
                    title = "曝光",
                    value = "${if (settings.exposureCompensation >= 0) "+" else ""}${settings.exposureCompensation}",
                    icon = Icons.Default.Exposure,
                    onValueChange = { /* TODO: 实现曝光补偿调整 */ }
                ) {
                    Slider(
                        value = settings.exposureCompensation.toFloat(),
                        onValueChange = { viewModel.updateExposureCompensation(it.toInt()) },
                        valueRange = -6f..6f,
                        steps = 12,
                        colors = SliderDefaults.colors(
                            thumbColor = Color.White,
                            activeTrackColor = Color.White,
                            inactiveTrackColor = Color.White.copy(alpha = 0.3f)
                        )
                    )
                }
                
                // 白平衡控制
                ProfessionalControl(
                    title = "白平衡",
                    value = settings.whiteBalance.displayName,
                    icon = Icons.Default.WbSunny,
                    onValueChange = { /* TODO: 实现白平衡调整 */ }
                ) {
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        WhiteBalance.values().forEach { wb ->
                            FilterChip(
                                onClick = { viewModel.updateWhiteBalance(wb) },
                                label = { Text(wb.displayName) },
                                selected = settings.whiteBalance == wb,
                                colors = FilterChipDefaults.filterChipColors(
                                    selectedContainerColor = Color.White,
                                    selectedLabelColor = Color.Black,
                                    containerColor = Color.Transparent,
                                    labelColor = Color.White
                                )
                            )
                        }
                    }
                }
                
                // 对焦模式
                ProfessionalControl(
                    title = "对焦",
                    value = "自动",
                    icon = Icons.Default.CenterFocusStrong,
                    onValueChange = { /* TODO: 实现对焦模式调整 */ }
                ) {
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        listOf("自动", "手动", "无限远").forEach { mode ->
                            FilterChip(
                                onClick = { /* TODO */ },
                                label = { Text(mode) },
                                selected = mode == "自动",
                                colors = FilterChipDefaults.filterChipColors(
                                    selectedContainerColor = Color.White,
                                    selectedLabelColor = Color.Black,
                                    containerColor = Color.Transparent,
                                    labelColor = Color.White
                                )
                            )
                        }
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.weight(1f))
        
        // 底部提示
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(8.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.6f)
            )
        ) {
            Text(
                text = "专业模式允许您手动控制相机参数，获得更精确的拍摄效果",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White.copy(alpha = 0.8f),
                modifier = Modifier.padding(12.dp)
            )
        }
    }
}

/**
 * 专业控制组件
 */
@Composable
private fun ProfessionalControl(
    title: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    onValueChange: () -> Unit,
    content: @Composable () -> Unit
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color.White,
                    fontWeight = FontWeight.Medium
                )
            }
            
            Text(
                text = value,
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White.copy(alpha = 0.8f)
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        content()
    }
}
