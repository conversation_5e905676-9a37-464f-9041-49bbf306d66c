package com.example.tcs.utils

import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import androidx.core.content.FileProvider
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

/**
 * 媒体文件工具类
 */
object MediaUtils {
    
    private const val FILENAME_FORMAT = "yyyy-MM-dd-HH-mm-ss-SSS"
    private const val IMAGE_MIME_TYPE = "image/jpeg"
    private const val VIDEO_MIME_TYPE = "video/mp4"
    
    /**
     * 保存图片到相册
     */
    fun saveImageToGallery(
        context: Context,
        bitmap: Bitmap,
        filename: String? = null
    ): Uri? {
        val displayName = filename ?: "IMG_${SimpleDateFormat(FILENAME_FORMAT, Locale.US).format(Date())}.jpg"
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            saveImageToMediaStore(context, bitmap, displayName)
        } else {
            saveImageToExternalStorage(context, bitmap, displayName)
        }
    }
    
    /**
     * 使用MediaStore保存图片（Android 10+）
     */
    private fun saveImageToMediaStore(
        context: Context,
        bitmap: Bitmap,
        displayName: String
    ): Uri? {
        val contentValues = ContentValues().apply {
            put(MediaStore.MediaColumns.DISPLAY_NAME, displayName)
            put(MediaStore.MediaColumns.MIME_TYPE, IMAGE_MIME_TYPE)
            put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_PICTURES)
        }
        
        val resolver = context.contentResolver
        val uri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
        
        return uri?.let {
            try {
                resolver.openOutputStream(it)?.use { outputStream ->
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 95, outputStream)
                }
                it
            } catch (e: IOException) {
                resolver.delete(it, null, null)
                null
            }
        }
    }
    
    /**
     * 保存图片到外部存储（Android 9及以下）
     */
    private fun saveImageToExternalStorage(
        context: Context,
        bitmap: Bitmap,
        displayName: String
    ): Uri? {
        val picturesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
        val imageFile = File(picturesDir, displayName)
        
        return try {
            FileOutputStream(imageFile).use { outputStream ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 95, outputStream)
            }
            
            // 通知媒体扫描器
            val contentValues = ContentValues().apply {
                put(MediaStore.MediaColumns.DATA, imageFile.absolutePath)
                put(MediaStore.MediaColumns.MIME_TYPE, IMAGE_MIME_TYPE)
            }
            context.contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
        } catch (e: IOException) {
            null
        }
    }
    
    /**
     * 创建临时图片文件
     */
    fun createTempImageFile(context: Context): File {
        val timeStamp = SimpleDateFormat(FILENAME_FORMAT, Locale.US).format(Date())
        val imageFileName = "TEMP_IMG_$timeStamp"
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return File.createTempFile(imageFileName, ".jpg", storageDir)
    }
    
    /**
     * 创建临时视频文件
     */
    fun createTempVideoFile(context: Context): File {
        val timeStamp = SimpleDateFormat(FILENAME_FORMAT, Locale.US).format(Date())
        val videoFileName = "TEMP_VID_$timeStamp"
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_MOVIES)
        return File.createTempFile(videoFileName, ".mp4", storageDir)
    }
    
    /**
     * 获取文件的Uri（用于分享）
     */
    fun getFileUri(context: Context, file: File): Uri {
        return FileProvider.getUriForFile(
            context,
            "${context.packageName}.fileprovider",
            file
        )
    }
    
    /**
     * 删除文件
     */
    fun deleteFile(file: File): Boolean {
        return try {
            file.delete()
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取文件大小（格式化）
     */
    fun getFormattedFileSize(sizeBytes: Long): String {
        val kb = sizeBytes / 1024.0
        val mb = kb / 1024.0
        val gb = mb / 1024.0
        
        return when {
            gb >= 1 -> String.format("%.1f GB", gb)
            mb >= 1 -> String.format("%.1f MB", mb)
            kb >= 1 -> String.format("%.1f KB", kb)
            else -> "$sizeBytes B"
        }
    }
    
    /**
     * 检查文件是否为图片
     */
    fun isImageFile(filename: String): Boolean {
        val imageExtensions = listOf("jpg", "jpeg", "png", "gif", "bmp", "webp")
        val extension = filename.substringAfterLast('.', "").lowercase()
        return extension in imageExtensions
    }
    
    /**
     * 检查文件是否为视频
     */
    fun isVideoFile(filename: String): Boolean {
        val videoExtensions = listOf("mp4", "avi", "mov", "wmv", "flv", "webm", "mkv")
        val extension = filename.substringAfterLast('.', "").lowercase()
        return extension in videoExtensions
    }
    
    /**
     * 获取媒体文件的MIME类型
     */
    fun getMimeType(filename: String): String {
        return when {
            isImageFile(filename) -> "image/*"
            isVideoFile(filename) -> "video/*"
            else -> "*/*"
        }
    }
    
    /**
     * 清理临时文件
     */
    fun cleanupTempFiles(context: Context) {
        try {
            // 清理临时图片文件
            context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)?.listFiles()?.forEach { file ->
                if (file.name.startsWith("TEMP_IMG_") && file.lastModified() < System.currentTimeMillis() - 24 * 60 * 60 * 1000) {
                    file.delete()
                }
            }
            
            // 清理临时视频文件
            context.getExternalFilesDir(Environment.DIRECTORY_MOVIES)?.listFiles()?.forEach { file ->
                if (file.name.startsWith("TEMP_VID_") && file.lastModified() < System.currentTimeMillis() - 24 * 60 * 60 * 1000) {
                    file.delete()
                }
            }
        } catch (e: Exception) {
            // 忽略清理错误
        }
    }
}
