package com.example.tcs.ui.timelapse

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay

/**
 * 延时摄影界面
 */
@Composable
fun TimeLapseScreen(
    onNavigateBack: () -> Unit,
    onStartCapture: (TimeLapseSettings) -> Unit
) {
    var isRecording by remember { mutableStateOf(false) }
    var capturedFrames by remember { mutableStateOf(0) }
    var elapsedTime by remember { mutableStateOf(0L) }
    var settings by remember { mutableStateOf(TimeLapseSettings()) }
    
    // 计时器
    LaunchedEffect(isRecording) {
        if (isRecording) {
            while (isRecording) {
                delay(1000)
                elapsedTime += 1000
                if (elapsedTime % (settings.intervalSeconds * 1000) == 0L) {
                    capturedFrames++
                }
            }
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
            .padding(16.dp)
    ) {
        // 标题栏
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "返回",
                    tint = Color.White
                )
            }
            
            Text(
                text = "延时摄影",
                style = MaterialTheme.typography.titleLarge,
                color = Color.White,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.width(48.dp))
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 状态显示
        if (isRecording) {
            RecordingStatus(
                capturedFrames = capturedFrames,
                elapsedTime = elapsedTime,
                settings = settings
            )
        } else {
            SettingsPanel(
                settings = settings,
                onSettingsChange = { settings = it }
            )
        }
        
        Spacer(modifier = Modifier.weight(1f))
        
        // 控制按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (isRecording) {
                // 停止按钮
                Button(
                    onClick = {
                        isRecording = false
                        elapsedTime = 0
                        capturedFrames = 0
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Red
                    ),
                    modifier = Modifier.size(80.dp),
                    shape = CircleShape
                ) {
                    Icon(
                        imageVector = Icons.Default.Stop,
                        contentDescription = "停止",
                        tint = Color.White,
                        modifier = Modifier.size(32.dp)
                    )
                }
            } else {
                // 开始按钮
                Button(
                    onClick = {
                        isRecording = true
                        onStartCapture(settings)
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.White
                    ),
                    modifier = Modifier.size(80.dp),
                    shape = CircleShape
                ) {
                    Icon(
                        imageVector = Icons.Default.PlayArrow,
                        contentDescription = "开始",
                        tint = Color.Black,
                        modifier = Modifier.size(32.dp)
                    )
                }
            }
        }
    }
}

/**
 * 录制状态显示
 */
@Composable
private fun RecordingStatus(
    capturedFrames: Int,
    elapsedTime: Long,
    settings: TimeLapseSettings
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.6f)
        )
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 录制指示器
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .size(12.dp)
                        .background(Color.Red, CircleShape)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "正在录制",
                    style = MaterialTheme.typography.titleMedium,
                    color = Color.White,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 统计信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatusItem(
                    title = "已拍摄",
                    value = "$capturedFrames 帧"
                )
                StatusItem(
                    title = "已用时",
                    value = formatTime(elapsedTime)
                )
                StatusItem(
                    title = "间隔",
                    value = "${settings.intervalSeconds}s"
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 预计视频时长
            val estimatedDuration = capturedFrames / 30.0 // 假设30fps
            Text(
                text = "预计视频时长: ${String.format("%.1f", estimatedDuration)}秒",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White.copy(alpha = 0.8f)
            )
        }
    }
}

/**
 * 设置面板
 */
@Composable
private fun SettingsPanel(
    settings: TimeLapseSettings,
    onSettingsChange: (TimeLapseSettings) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.6f)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "延时摄影设置",
                style = MaterialTheme.typography.titleMedium,
                color = Color.White,
                fontWeight = FontWeight.Bold
            )
            
            // 拍摄间隔
            SettingItem(
                title = "拍摄间隔",
                value = "${settings.intervalSeconds}秒"
            ) {
                Slider(
                    value = settings.intervalSeconds.toFloat(),
                    onValueChange = { 
                        onSettingsChange(settings.copy(intervalSeconds = it.toInt()))
                    },
                    valueRange = 1f..60f,
                    steps = 59,
                    colors = SliderDefaults.colors(
                        thumbColor = Color.White,
                        activeTrackColor = Color.White,
                        inactiveTrackColor = Color.White.copy(alpha = 0.3f)
                    )
                )
            }
            
            // 总时长
            SettingItem(
                title = "总时长",
                value = "${settings.durationMinutes}分钟"
            ) {
                Slider(
                    value = settings.durationMinutes.toFloat(),
                    onValueChange = { 
                        onSettingsChange(settings.copy(durationMinutes = it.toInt()))
                    },
                    valueRange = 1f..120f,
                    steps = 119,
                    colors = SliderDefaults.colors(
                        thumbColor = Color.White,
                        activeTrackColor = Color.White,
                        inactiveTrackColor = Color.White.copy(alpha = 0.3f)
                    )
                )
            }
            
            // 预览信息
            val totalFrames = (settings.durationMinutes * 60) / settings.intervalSeconds
            val videoDuration = totalFrames / 30.0
            
            Text(
                text = "将拍摄 $totalFrames 帧，生成约 ${String.format("%.1f", videoDuration)} 秒的视频",
                style = MaterialTheme.typography.bodySmall,
                color = Color.White.copy(alpha = 0.7f)
            )
        }
    }
}

/**
 * 状态项
 */
@Composable
private fun StatusItem(
    title: String,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleLarge,
            color = Color.White,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = title,
            style = MaterialTheme.typography.bodySmall,
            color = Color.White.copy(alpha = 0.7f)
        )
    }
}

/**
 * 设置项
 */
@Composable
private fun SettingItem(
    title: String,
    value: String,
    content: @Composable () -> Unit
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                color = Color.White
            )
            Text(
                text = value,
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White.copy(alpha = 0.8f)
            )
        }
        Spacer(modifier = Modifier.height(8.dp))
        content()
    }
}

/**
 * 格式化时间
 */
private fun formatTime(timeMs: Long): String {
    val seconds = timeMs / 1000
    val minutes = seconds / 60
    val hours = minutes / 60
    return String.format("%02d:%02d:%02d", hours, minutes % 60, seconds % 60)
}

/**
 * 延时摄影设置
 */
data class TimeLapseSettings(
    val intervalSeconds: Int = 5,
    val durationMinutes: Int = 10
)
