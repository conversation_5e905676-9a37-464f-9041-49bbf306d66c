package com.example.tcs.camera

import android.content.Context
import android.net.Uri
import android.util.Log
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.video.*
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.example.tcs.data.CameraSettings
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * 相机控制器 - 管理相机的核心功能
 */
class CameraController(private val context: Context) {
    
    private var cameraProvider: ProcessCameraProvider? = null
    private var camera: Camera? = null
    private var preview: Preview? = null
    private var imageCapture: ImageCapture? = null
    private var videoCapture: VideoCapture<Recorder>? = null
    private var recording: Recording? = null
    
    private val cameraExecutor: ExecutorService = Executors.newSingleThreadExecutor()
    
    // 相机状态
    private val _cameraState = MutableStateFlow(CameraState.IDLE)
    val cameraState: StateFlow<CameraState> = _cameraState.asStateFlow()
    
    // 相机设置
    private val _settings = MutableStateFlow(CameraSettings())
    val settings: StateFlow<CameraSettings> = _settings.asStateFlow()
    
    // 错误状态
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    companion object {
        private const val TAG = "CameraController"
        private const val FILENAME_FORMAT = "yyyy-MM-dd-HH-mm-ss-SSS"
    }
    
    /**
     * 初始化相机
     */
    suspend fun initializeCamera(
        lifecycleOwner: LifecycleOwner,
        previewView: PreviewView
    ) {
        try {
            _cameraState.value = CameraState.INITIALIZING
            
            cameraProvider = ProcessCameraProvider.getInstance(context).get()
            
            setupCamera(lifecycleOwner, previewView)
            
            _cameraState.value = CameraState.READY
        } catch (e: Exception) {
            Log.e(TAG, "Camera initialization failed", e)
            _error.value = "相机初始化失败: ${e.message}"
            _cameraState.value = CameraState.ERROR
        }
    }
    
    /**
     * 设置相机
     */
    private fun setupCamera(
        lifecycleOwner: LifecycleOwner,
        previewView: PreviewView
    ) {
        val currentSettings = _settings.value
        
        // 创建预览用例
        preview = Preview.Builder()
            .setTargetAspectRatio(currentSettings.aspectRatio)
            .build()
            .also {
                it.setSurfaceProvider(previewView.surfaceProvider)
            }
        
        // 创建图像捕获用例
        imageCapture = ImageCapture.Builder()
            .setTargetAspectRatio(currentSettings.aspectRatio)
            .setFlashMode(currentSettings.flashMode)
            .build()
        
        // 创建视频捕获用例
        val recorder = Recorder.Builder()
            .setQualitySelector(QualitySelector.from(Quality.HIGHEST))
            .build()
        videoCapture = VideoCapture.withOutput(recorder)
        
        // 选择相机
        val cameraSelector = CameraSelector.Builder()
            .requireLensFacing(currentSettings.lensFacing)
            .build()
        
        try {
            // 解绑所有用例
            cameraProvider?.unbindAll()
            
            // 绑定用例到相机
            camera = cameraProvider?.bindToLifecycle(
                lifecycleOwner,
                cameraSelector,
                preview,
                imageCapture,
                videoCapture
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Camera binding failed", e)
            throw e
        }
    }
    
    /**
     * 拍照
     */
    fun takePhoto(onResult: (Boolean, Uri?, String?) -> Unit) {
        val imageCapture = imageCapture ?: run {
            onResult(false, null, "相机未初始化")
            return
        }
        
        _cameraState.value = CameraState.CAPTURING
        
        // 创建输出文件
        val photoFile = File(
            context.getExternalFilesDir(null),
            "IMG_${SimpleDateFormat(FILENAME_FORMAT, Locale.US).format(System.currentTimeMillis())}.jpg"
        )
        
        val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()
        
        imageCapture.takePicture(
            outputOptions,
            ContextCompat.getMainExecutor(context),
            object : ImageCapture.OnImageSavedCallback {
                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                    _cameraState.value = CameraState.READY
                    onResult(true, Uri.fromFile(photoFile), null)
                }
                
                override fun onError(exception: ImageCaptureException) {
                    _cameraState.value = CameraState.READY
                    Log.e(TAG, "Photo capture failed", exception)
                    onResult(false, null, "拍照失败: ${exception.message}")
                }
            }
        )
    }
    
    /**
     * 开始录像
     */
    fun startVideoRecording(onResult: (Boolean, String?) -> Unit) {
        val videoCapture = videoCapture ?: run {
            onResult(false, "相机未初始化")
            return
        }
        
        if (recording != null) {
            onResult(false, "正在录像中")
            return
        }
        
        _cameraState.value = CameraState.RECORDING
        
        val videoFile = File(
            context.getExternalFilesDir(null),
            "VID_${SimpleDateFormat(FILENAME_FORMAT, Locale.US).format(System.currentTimeMillis())}.mp4"
        )
        
        val outputOptions = FileOutputOptions.Builder(videoFile).build()
        
        recording = videoCapture.output
            .prepareRecording(context, outputOptions)
            .start(ContextCompat.getMainExecutor(context)) { recordEvent ->
                when (recordEvent) {
                    is VideoRecordEvent.Start -> {
                        onResult(true, null)
                    }
                    is VideoRecordEvent.Finalize -> {
                        if (!recordEvent.hasError()) {
                            _cameraState.value = CameraState.READY
                            onResult(true, "录像保存成功")
                        } else {
                            _cameraState.value = CameraState.READY
                            onResult(false, "录像失败: ${recordEvent.error}")
                        }
                        recording = null
                    }
                }
            }
    }
    
    /**
     * 停止录像
     */
    fun stopVideoRecording() {
        recording?.stop()
        recording = null
        _cameraState.value = CameraState.READY
    }
    
    /**
     * 切换相机
     */
    fun switchCamera(lifecycleOwner: LifecycleOwner, previewView: PreviewView) {
        val currentSettings = _settings.value
        val newLensFacing = if (currentSettings.lensFacing == CameraSelector.LENS_FACING_BACK) {
            CameraSelector.LENS_FACING_FRONT
        } else {
            CameraSelector.LENS_FACING_BACK
        }
        
        updateSettings(currentSettings.copy(lensFacing = newLensFacing))
        setupCamera(lifecycleOwner, previewView)
    }
    
    /**
     * 更新设置
     */
    fun updateSettings(newSettings: CameraSettings) {
        _settings.value = newSettings
    }
    
    /**
     * 释放资源
     */
    fun release() {
        cameraExecutor.shutdown()
        recording?.stop()
    }
}

/**
 * 相机状态
 */
enum class CameraState {
    IDLE,           // 空闲
    INITIALIZING,   // 初始化中
    READY,          // 就绪
    CAPTURING,      // 拍照中
    RECORDING,      // 录像中
    ERROR           // 错误
}
