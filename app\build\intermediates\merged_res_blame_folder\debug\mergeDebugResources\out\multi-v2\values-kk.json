{"logs": [{"outputFile": "com.example.tcs.app-mergeDebugResources-67:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e754f461ad8f7a511fa7fc6c4385c693\\transformed\\material3-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,406,522,624,725,843,981,1106,1231,1315,1418,1508,1605,1721,1845,1953,2095,2235,2367,2526,2649,2764,2883,2998,3089,3187,3310,3445,3549,3660,3766,3905,4050,4158,4258,4344,4437,4530,4638,4724,4808,4912,5001,5086,5187,5291,5388,5484,5571,5675,5774,5872,6009,6099,6210", "endColumns": "117,117,114,115,101,100,117,137,124,124,83,102,89,96,115,123,107,141,139,131,158,122,114,118,114,90,97,122,134,103,110,105,138,144,107,99,85,92,92,107,85,83,103,88,84,100,103,96,95,86,103,98,97,136,89,110,100", "endOffsets": "168,286,401,517,619,720,838,976,1101,1226,1310,1413,1503,1600,1716,1840,1948,2090,2230,2362,2521,2644,2759,2878,2993,3084,3182,3305,3440,3544,3655,3761,3900,4045,4153,4253,4339,4432,4525,4633,4719,4803,4907,4996,5081,5182,5286,5383,5479,5566,5670,5769,5867,6004,6094,6205,6306"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4159,4277,4395,4510,4626,4728,4829,4947,5085,5210,5335,5419,5522,5612,5709,5825,5949,6057,6199,6339,6471,6630,6753,6868,6987,7102,7193,7291,7414,7549,7653,7764,7870,8009,8154,8262,8362,8448,8541,8634,8742,8828,8912,9016,9105,9190,9291,9395,9492,9588,9675,9779,9878,9976,10113,10203,10314", "endColumns": "117,117,114,115,101,100,117,137,124,124,83,102,89,96,115,123,107,141,139,131,158,122,114,118,114,90,97,122,134,103,110,105,138,144,107,99,85,92,92,107,85,83,103,88,84,100,103,96,95,86,103,98,97,136,89,110,100", "endOffsets": "4272,4390,4505,4621,4723,4824,4942,5080,5205,5330,5414,5517,5607,5704,5820,5944,6052,6194,6334,6466,6625,6748,6863,6982,7097,7188,7286,7409,7544,7648,7759,7865,8004,8149,8257,8357,8443,8536,8629,8737,8823,8907,9011,9100,9185,9286,9390,9487,9583,9670,9774,9873,9971,10108,10198,10309,10410"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae455f1a13db310449e207b49d3bacc\\transformed\\appcompat-1.1.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,896,987,1079,1174,1268,1369,1462,1557,1654,1745,1836,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,77,90,91,94,93,100,92,94,96,90,90,79,104,102,97,106,105,99,165,94,80", "endOffsets": "208,313,423,508,614,733,813,891,982,1074,1169,1263,1364,1457,1552,1649,1740,1831,1911,2016,2119,2217,2324,2430,2530,2696,2791,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,896,987,1079,1174,1268,1369,1462,1557,1654,1745,1836,1916,2021,2124,2222,2329,2435,2535,2701,10755", "endColumns": "107,104,109,84,105,118,79,77,90,91,94,93,100,92,94,96,90,90,79,104,102,97,106,105,99,165,94,80", "endOffsets": "208,313,423,508,614,733,813,891,982,1074,1169,1263,1364,1457,1552,1649,1740,1831,1911,2016,2119,2217,2324,2430,2530,2696,2791,10831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3109e90bbcb133a6123d6a171e482e61\\transformed\\foundation-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,147", "endColumns": "91,95", "endOffsets": "142,238"}, "to": {"startLines": "113,114", "startColumns": "4,4", "startOffsets": "11498,11590", "endColumns": "91,95", "endOffsets": "11585,11681"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\eb5978be55e4593326b90344069d1b2d\\transformed\\ui-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,386,488,575,656,749,839,921,1004,1089,1162,1236,1312,1386,1462,1532", "endColumns": "92,82,104,101,86,80,92,89,81,82,84,72,73,75,73,75,69,117", "endOffsets": "193,276,381,483,570,651,744,834,916,999,1084,1157,1231,1307,1381,1457,1527,1645"}, "to": {"startLines": "36,37,38,39,40,41,42,100,101,102,103,105,106,107,108,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3515,3608,3691,3796,3898,3985,4066,10415,10505,10587,10670,10836,10909,10983,11059,11234,11310,11380", "endColumns": "92,82,104,101,86,80,92,89,81,82,84,72,73,75,73,75,69,117", "endOffsets": "3603,3686,3791,3893,3980,4061,4154,10500,10582,10665,10750,10904,10978,11054,11128,11305,11375,11493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d0e5df3da8374bed71d9993cb807cb2d\\transformed\\core-1.16.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,359,462,566,663,774", "endColumns": "99,101,101,102,103,96,110,100", "endOffsets": "150,252,354,457,561,658,769,870"}, "to": {"startLines": "29,30,31,32,33,34,35,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2796,2896,2998,3100,3203,3307,3404,11133", "endColumns": "99,101,101,102,103,96,110,100", "endOffsets": "2891,2993,3095,3198,3302,3399,3510,11229"}}]}]}