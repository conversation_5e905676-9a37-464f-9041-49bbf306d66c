package com.example.tcs.viewmodel

import android.app.Application
import android.net.Uri
import androidx.camera.view.PreviewView
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.viewModelScope
import com.example.tcs.camera.CameraController
import com.example.tcs.camera.CameraState
import com.example.tcs.data.CameraSettings
import com.example.tcs.data.CaptureMode
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

/**
 * 相机ViewModel - 管理相机状态和业务逻辑
 */
class CameraViewModel(application: Application) : AndroidViewModel(application) {
    
    private val cameraController = CameraController(application)
    
    // 暴露相机状态
    val cameraState: StateFlow<CameraState> = cameraController.cameraState
    val settings: StateFlow<CameraSettings> = cameraController.settings
    val error: StateFlow<String?> = cameraController.error
    
    /**
     * 初始化相机
     */
    fun initializeCamera(lifecycleOwner: LifecycleOwner, previewView: PreviewView) {
        viewModelScope.launch {
            cameraController.initializeCamera(lifecycleOwner, previewView)
        }
    }
    
    /**
     * 拍照
     */
    fun takePhoto(onResult: (Boolean, Uri?, String?) -> Unit) {
        cameraController.takePhoto(onResult)
    }
    
    /**
     * 开始录像
     */
    fun startVideoRecording(onResult: (Boolean, String?) -> Unit) {
        cameraController.startVideoRecording(onResult)
    }
    
    /**
     * 停止录像
     */
    fun stopVideoRecording() {
        cameraController.stopVideoRecording()
    }
    
    /**
     * 切换相机
     */
    fun switchCamera(lifecycleOwner: LifecycleOwner) {
        // 这里需要传递previewView，但由于架构限制，我们需要重新设计
        // 暂时留空，在实际使用时需要传递previewView
    }
    
    /**
     * 更新闪光灯模式
     */
    fun updateFlashMode(flashMode: Int) {
        val currentSettings = settings.value
        cameraController.updateSettings(currentSettings.copy(flashMode = flashMode))
    }
    
    /**
     * 更新拍摄模式
     */
    fun updateCaptureMode(captureMode: CaptureMode) {
        val currentSettings = settings.value
        cameraController.updateSettings(currentSettings.copy(captureMode = captureMode))
    }
    
    /**
     * 更新宽高比
     */
    fun updateAspectRatio(aspectRatio: Int) {
        val currentSettings = settings.value
        cameraController.updateSettings(currentSettings.copy(aspectRatio = aspectRatio))
    }
    
    /**
     * 更新网格线显示
     */
    fun updateGridLines(enabled: Boolean) {
        val currentSettings = settings.value
        cameraController.updateSettings(currentSettings.copy(gridLines = enabled))
    }
    
    /**
     * 更新定时器
     */
    fun updateTimer(timerDelay: com.example.tcs.data.TimerDelay) {
        val currentSettings = settings.value
        cameraController.updateSettings(currentSettings.copy(timerDelay = timerDelay))
    }
    
    /**
     * 更新HDR
     */
    fun updateHDR(enabled: Boolean) {
        val currentSettings = settings.value
        cameraController.updateSettings(currentSettings.copy(hdr = enabled))
    }
    
    /**
     * 更新防抖
     */
    fun updateStabilization(enabled: Boolean) {
        val currentSettings = settings.value
        cameraController.updateSettings(currentSettings.copy(stabilization = enabled))
    }
    
    /**
     * 更新滤镜
     */
    fun updateFilter(filter: com.example.tcs.data.CameraFilter) {
        val currentSettings = settings.value
        cameraController.updateSettings(currentSettings.copy(filter = filter))
    }
    
    /**
     * 更新图像质量
     */
    fun updateImageQuality(quality: com.example.tcs.data.ImageQuality) {
        val currentSettings = settings.value
        cameraController.updateSettings(currentSettings.copy(imageQuality = quality))
    }
    
    /**
     * 更新视频质量
     */
    fun updateVideoQuality(quality: com.example.tcs.data.VideoQuality) {
        val currentSettings = settings.value
        cameraController.updateSettings(currentSettings.copy(videoQuality = quality))
    }
    
    /**
     * 更新专业模式
     */
    fun updateManualMode(enabled: Boolean) {
        val currentSettings = settings.value
        cameraController.updateSettings(currentSettings.copy(manualMode = enabled))
    }
    
    /**
     * 更新ISO
     */
    fun updateISO(iso: Int) {
        val currentSettings = settings.value
        cameraController.updateSettings(currentSettings.copy(iso = iso))
    }
    
    /**
     * 更新曝光补偿
     */
    fun updateExposureCompensation(compensation: Int) {
        val currentSettings = settings.value
        cameraController.updateSettings(currentSettings.copy(exposureCompensation = compensation))
    }
    
    /**
     * 更新白平衡
     */
    fun updateWhiteBalance(whiteBalance: com.example.tcs.data.WhiteBalance) {
        val currentSettings = settings.value
        cameraController.updateSettings(currentSettings.copy(whiteBalance = whiteBalance))
    }
    
    /**
     * 更新声音设置
     */
    fun updateSoundEnabled(enabled: Boolean) {
        val currentSettings = settings.value
        cameraController.updateSettings(currentSettings.copy(soundEnabled = enabled))
    }
    
    /**
     * 更新位置标记
     */
    fun updateLocationTagging(enabled: Boolean) {
        val currentSettings = settings.value
        cameraController.updateSettings(currentSettings.copy(locationTagging = enabled))
    }
    
    /**
     * 更新前置相机镜像
     */
    fun updateMirrorFrontCamera(enabled: Boolean) {
        val currentSettings = settings.value
        cameraController.updateSettings(currentSettings.copy(mirrorFrontCamera = enabled))
    }
    
    /**
     * 重置设置为默认值
     */
    fun resetToDefaults() {
        cameraController.updateSettings(CameraSettings())
    }
    
    override fun onCleared() {
        super.onCleared()
        cameraController.release()
    }
}
