{"logs": [{"outputFile": "com.example.tcs.app-mergeDebugResources-67:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae455f1a13db310449e207b49d3bacc\\transformed\\appcompat-1.1.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,878,969,1062,1156,1250,1351,1444,1539,1632,1723,1815,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,78,90,92,93,93,100,92,94,92,90,91,79,104,102,97,104,101,101,153,96,80", "endOffsets": "205,305,413,497,599,715,794,873,964,1057,1151,1245,1346,1439,1534,1627,1718,1810,1890,1995,2098,2196,2301,2403,2505,2659,2756,2837"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,878,969,1062,1156,1250,1351,1444,1539,1632,1723,1815,1895,2000,2103,2201,2306,2408,2510,2664,10478", "endColumns": "104,99,107,83,101,115,78,78,90,92,93,93,100,92,94,92,90,91,79,104,102,97,104,101,101,153,96,80", "endOffsets": "205,305,413,497,599,715,794,873,964,1057,1151,1245,1346,1439,1534,1627,1718,1810,1890,1995,2098,2196,2301,2403,2505,2659,2756,10554"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d0e5df3da8374bed71d9993cb807cb2d\\transformed\\core-1.16.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "29,30,31,32,33,34,35,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2761,2855,2957,3054,3151,3252,3352,10856", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "2850,2952,3049,3146,3247,3347,3453,10952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\eb5978be55e4593326b90344069d1b2d\\transformed\\ui-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,278,371,466,549,626,711,797,876,954,1036,1105,1179,1257,1333,1407,1478", "endColumns": "88,83,92,94,82,76,84,85,78,77,81,68,73,77,75,73,70,118", "endOffsets": "189,273,366,461,544,621,706,792,871,949,1031,1100,1174,1252,1328,1402,1473,1592"}, "to": {"startLines": "36,37,38,39,40,41,42,100,101,102,103,105,106,107,108,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3458,3547,3631,3724,3819,3902,3979,10153,10239,10318,10396,10559,10628,10702,10780,10957,11031,11102", "endColumns": "88,83,92,94,82,76,84,85,78,77,81,68,73,77,75,73,70,118", "endOffsets": "3542,3626,3719,3814,3897,3974,4059,10234,10313,10391,10473,10623,10697,10775,10851,11026,11097,11216"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e754f461ad8f7a511fa7fc6c4385c693\\transformed\\material3-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,288,406,521,620,715,828,955,1070,1210,1295,1393,1484,1580,1697,1817,1920,2056,2191,2312,2465,2583,2693,2808,2926,3018,3115,3227,3351,3449,3548,3652,3786,3927,4034,4134,4215,4320,4424,4534,4620,4705,4808,4888,4972,5073,5172,5263,5358,5444,5546,5645,5742,5867,5947,6048", "endColumns": "116,115,117,114,98,94,112,126,114,139,84,97,90,95,116,119,102,135,134,120,152,117,109,114,117,91,96,111,123,97,98,103,133,140,106,99,80,104,103,109,85,84,102,79,83,100,98,90,94,85,101,98,96,124,79,100,95", "endOffsets": "167,283,401,516,615,710,823,950,1065,1205,1290,1388,1479,1575,1692,1812,1915,2051,2186,2307,2460,2578,2688,2803,2921,3013,3110,3222,3346,3444,3543,3647,3781,3922,4029,4129,4210,4315,4419,4529,4615,4700,4803,4883,4967,5068,5167,5258,5353,5439,5541,5640,5737,5862,5942,6043,6139"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4064,4181,4297,4415,4530,4629,4724,4837,4964,5079,5219,5304,5402,5493,5589,5706,5826,5929,6065,6200,6321,6474,6592,6702,6817,6935,7027,7124,7236,7360,7458,7557,7661,7795,7936,8043,8143,8224,8329,8433,8543,8629,8714,8817,8897,8981,9082,9181,9272,9367,9453,9555,9654,9751,9876,9956,10057", "endColumns": "116,115,117,114,98,94,112,126,114,139,84,97,90,95,116,119,102,135,134,120,152,117,109,114,117,91,96,111,123,97,98,103,133,140,106,99,80,104,103,109,85,84,102,79,83,100,98,90,94,85,101,98,96,124,79,100,95", "endOffsets": "4176,4292,4410,4525,4624,4719,4832,4959,5074,5214,5299,5397,5488,5584,5701,5821,5924,6060,6195,6316,6469,6587,6697,6812,6930,7022,7119,7231,7355,7453,7552,7656,7790,7931,8038,8138,8219,8324,8428,8538,8624,8709,8812,8892,8976,9077,9176,9267,9362,9448,9550,9649,9746,9871,9951,10052,10148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3109e90bbcb133a6123d6a171e482e61\\transformed\\foundation-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,87", "endOffsets": "137,225"}, "to": {"startLines": "113,114", "startColumns": "4,4", "startOffsets": "11221,11308", "endColumns": "86,87", "endOffsets": "11303,11391"}}]}]}