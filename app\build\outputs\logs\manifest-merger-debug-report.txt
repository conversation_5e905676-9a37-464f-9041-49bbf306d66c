-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from D:\tcs\app\src\main\AndroidManifest.xml:54:9-62:20
	android:grantUriPermissions
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:58:13-47
	android:authorities
		INJECTED from D:\tcs\app\src\main\AndroidManifest.xml
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:56:13-64
	android:exported
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:57:13-37
	android:name
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:55:13-62
manifest
ADDED from D:\tcs\app\src\main\AndroidManifest.xml:2:1-65:12
INJECTED from D:\tcs\app\src\main\AndroidManifest.xml:2:1-65:12
INJECTED from D:\tcs\app\src\main\AndroidManifest.xml:2:1-65:12
INJECTED from D:\tcs\app\src\main\AndroidManifest.xml:2:1-65:12
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e361213b42e113c1e72f284582aab74\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\9bac6ff2bb2956f26d27e84c234401f2\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f16fe121e751bf9655a32005d05688f\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\77ccd2f4b4e5ecdbc59283addde4bc74\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7780a4dcc82858bd1f00d2c10346682a\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5127253ec65a88b11d5b35d8474ce05\transformed\navigation-compose-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-extensions:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\73eda55041be236a16720a4a764f5ab1\transformed\camera-extensions-1.3.4\AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-video:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0adc9c7203c60e1aa5b9443e75c08d0\transformed\camera-video-1.3.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ae8756eeb9ad5bf91c354b1aa0d6a55\transformed\camera-lifecycle-1.3.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d0c1c396aebaa0e12210312a4aec0ec\transformed\camera-core-1.3.4\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5e89f9b119dd4ecc3de589db06b83c8\transformed\camera-view-1.3.4\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c7afca279dd5c12759426dbdd9350ef\transformed\coil-compose-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7d17141c8a63b83c246d06dffae4ad2\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\06778060c5dddb525115952c5b433cfe\transformed\coil-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8896984ea83f52ba9df2487dda1f9b\transformed\coil-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a69f5246a1a71e421f05951a759a13a\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85171e4206c16718d7c8d5bf43fe825d\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cd3daaaa7b2a32f64013d0258d8a30a\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e754f461ad8f7a511fa7fc6c4385c693\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\37aef1d4a50fb91f5b565408d177e4e2\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\499cb625e195031490d2b230fd99c673\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\306dbeeedef5ec3722ac3802108b58d4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9e631c8d1098161560685b7269fb152\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32b6106b20c62ce5414ad176bfa008e5\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6edc257433ad7f57ca34fc395a7be0d\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\69dd2d7e43f73ac56303f4f8edd67cfe\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1225ad1152007df66d50dcffcbe26e25\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ac64f4a5c821ae5d20223efeb7e5866\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9a938c84971bf6579aa7fdf761e25d5\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\368884b31e1d49d62cfc67bcb3df93c5\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a67d276a67a1709c240307a5ca7afdb8\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5655aeb3e3e3070e27621041d463bbe9\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f1b8466df86c6c8ca60bf674e7b73d7\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\561848234033d56cb459dd7112058163\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f61f6209fc0ff581f93622a9402fd8ef\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\3109e90bbcb133a6123d6a171e482e61\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7a10baaa6db82e55aa7059558ca8c00\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c436e683a15c443b97ae35b9b9f4740\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc60d967e3e96a9500d84b22b153d157\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a90a4a7ada7d52b4c6f88de75cc10b3\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e4d5a7b637c696b107f8ca7a1e781c7\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\015561b9d3a14a2b0d3725e010e34d2f\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\872671f11bcf03422d04045da95839b0\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d714ce27bb382dd5411af92c5af9fa71\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd811a106770e2ea84bb91143af278b4\transformed\lifecycle-viewmodel-2.9.2\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\54c8e97fceebf05a01c2f3ccec507e1e\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d22afa2cf35df91f286953a0d38f327c\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\82acd443bb8e6b620978dbc588e9d803\transformed\lifecycle-livedata-core-ktx-2.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbb2718579edd88a786d10712a0c8e9f\transformed\lifecycle-livedata-core-2.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9aa874683dd79c9062fac97bf4b6c412\transformed\savedstate-ktx-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ed89daf239d020270328bf0b2917631\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\750ad70ca7d2f2aadf98cdad3b4c7def\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7edce7b8ce0e805ee7581ac75045450a\transformed\lifecycle-viewmodel-ktx-2.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\13aec054922c6b865e26b9010d4084aa\transformed\lifecycle-livedata-2.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b88ce6832dcb1a9564fbc158fb43c8a3\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e6de996d8b54044ba9ed8de4a509bd7\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2b3656dd21bf3a3769f9616474f9651\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ebcffb00f1687fc3e2fd5e4a23fcae64\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb5978be55e4593326b90344069d1b2d\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c756c9cce9b2c5b631b4841f4f3a425a\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\464fb997f86c74cbb89077e01e66c511\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a70232a586e72f145ee1e22636868bba\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2bbbf4b431026cb8c4d172599690ff63\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c19c93bf4b3ae41606569ce25471910a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2ae42fb96d605594219ee072ea3d24b\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aec2f011c027afa72a7ad78ce4640a13\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2308300ca59644f38669bf11db853be\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3dd9e70c81c9ac7c3c96a466ada66e88\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\91a0e68982f831fac00972bf1238b759\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\a161a899397881ed0a3da01ece1dbb8f\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08676af66e63b63b9c97decd248ae0ed\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f9fffeca9b4e75d900913b18ac2df78\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\748dcec305cccd386b1adf80082d9293\transformed\napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c101beef2568fba5283b14c16a1e818\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d6d2a8a7272352c082e2050a2b41847\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\59782c46e92902f9e632069eb2572923\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\268192141123e01386248e6368401d3e\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb891f82f60120b0609f05d68d17a84f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3b6530e7271de0fec1178f5d19b403e\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b30944cffca17a947fd6a78050968f33\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from D:\tcs\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\tcs\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\tcs\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.CAMERA
ADDED from D:\tcs\app\src\main\AndroidManifest.xml:6:5-65
	android:name
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:6:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from D:\tcs\app\src\main\AndroidManifest.xml:7:5-71
	android:name
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:7:22-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\tcs\app\src\main\AndroidManifest.xml:10:5-11:38
	android:maxSdkVersion
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:11:9-35
	android:name
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:10:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\tcs\app\src\main\AndroidManifest.xml:12:5-13:38
	android:maxSdkVersion
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:13:9-35
	android:name
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:12:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from D:\tcs\app\src\main\AndroidManifest.xml:14:5-76
	android:name
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:14:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from D:\tcs\app\src\main\AndroidManifest.xml:15:5-75
	android:name
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:15:22-72
uses-feature#android.hardware.camera
ADDED from D:\tcs\app\src\main\AndroidManifest.xml:18:5-20:35
	android:required
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:20:9-32
	android:name
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:19:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from D:\tcs\app\src\main\AndroidManifest.xml:21:5-23:36
	android:required
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:23:9-33
	android:name
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:22:9-57
uses-feature#android.hardware.camera.flash
ADDED from D:\tcs\app\src\main\AndroidManifest.xml:24:5-26:36
	android:required
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:26:9-33
	android:name
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:25:9-53
uses-feature#android.hardware.camera.front
ADDED from D:\tcs\app\src\main\AndroidManifest.xml:27:5-29:36
	android:required
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:29:9-33
	android:name
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:28:9-53
application
ADDED from D:\tcs\app\src\main\AndroidManifest.xml:31:5-63:19
INJECTED from D:\tcs\app\src\main\AndroidManifest.xml:31:5-63:19
MERGED from [androidx.camera:camera-extensions:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\73eda55041be236a16720a4a764f5ab1\transformed\camera-extensions-1.3.4\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\73eda55041be236a16720a4a764f5ab1\transformed\camera-extensions-1.3.4\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d0c1c396aebaa0e12210312a4aec0ec\transformed\camera-core-1.3.4\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d0c1c396aebaa0e12210312a4aec0ec\transformed\camera-core-1.3.4\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\368884b31e1d49d62cfc67bcb3df93c5\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\368884b31e1d49d62cfc67bcb3df93c5\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c756c9cce9b2c5b631b4841f4f3a425a\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c756c9cce9b2c5b631b4841f4f3a425a\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\59782c46e92902f9e632069eb2572923\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\59782c46e92902f9e632069eb2572923\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb891f82f60120b0609f05d68d17a84f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb891f82f60120b0609f05d68d17a84f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\tcs\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:38:9-35
	android:label
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:36:9-41
	android:fullBackupContent
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:34:9-54
	android:roundIcon
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:37:9-54
	android:icon
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:35:9-43
	android:allowBackup
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:32:9-35
	android:theme
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:39:9-41
	android:dataExtractionRules
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:33:9-65
activity#com.example.tcs.MainActivity
ADDED from D:\tcs\app\src\main\AndroidManifest.xml:40:9-51:20
	android:screenOrientation
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:45:13-49
	android:label
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:43:13-45
	android:exported
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:42:13-36
	android:theme
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:44:13-45
	android:name
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:41:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\tcs\app\src\main\AndroidManifest.xml:46:13-50:29
action#android.intent.action.MAIN
ADDED from D:\tcs\app\src\main\AndroidManifest.xml:47:17-69
	android:name
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:47:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\tcs\app\src\main\AndroidManifest.xml:49:17-77
	android:name
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:49:27-74
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\tcs\app\src\main\AndroidManifest.xml:59:13-61:54
	android:resource
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:61:17-51
	android:name
		ADDED from D:\tcs\app\src\main\AndroidManifest.xml:60:17-67
uses-sdk
INJECTED from D:\tcs\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\tcs\app\src\main\AndroidManifest.xml
INJECTED from D:\tcs\app\src\main\AndroidManifest.xml
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e361213b42e113c1e72f284582aab74\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e361213b42e113c1e72f284582aab74\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\9bac6ff2bb2956f26d27e84c234401f2\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\9bac6ff2bb2956f26d27e84c234401f2\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f16fe121e751bf9655a32005d05688f\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f16fe121e751bf9655a32005d05688f\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\77ccd2f4b4e5ecdbc59283addde4bc74\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\77ccd2f4b4e5ecdbc59283addde4bc74\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7780a4dcc82858bd1f00d2c10346682a\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7780a4dcc82858bd1f00d2c10346682a\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5127253ec65a88b11d5b35d8474ce05\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5127253ec65a88b11d5b35d8474ce05\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-extensions:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\73eda55041be236a16720a4a764f5ab1\transformed\camera-extensions-1.3.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\73eda55041be236a16720a4a764f5ab1\transformed\camera-extensions-1.3.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-video:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0adc9c7203c60e1aa5b9443e75c08d0\transformed\camera-video-1.3.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0adc9c7203c60e1aa5b9443e75c08d0\transformed\camera-video-1.3.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ae8756eeb9ad5bf91c354b1aa0d6a55\transformed\camera-lifecycle-1.3.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ae8756eeb9ad5bf91c354b1aa0d6a55\transformed\camera-lifecycle-1.3.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d0c1c396aebaa0e12210312a4aec0ec\transformed\camera-core-1.3.4\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d0c1c396aebaa0e12210312a4aec0ec\transformed\camera-core-1.3.4\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5e89f9b119dd4ecc3de589db06b83c8\transformed\camera-view-1.3.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5e89f9b119dd4ecc3de589db06b83c8\transformed\camera-view-1.3.4\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c7afca279dd5c12759426dbdd9350ef\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c7afca279dd5c12759426dbdd9350ef\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7d17141c8a63b83c246d06dffae4ad2\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7d17141c8a63b83c246d06dffae4ad2\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\06778060c5dddb525115952c5b433cfe\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\06778060c5dddb525115952c5b433cfe\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8896984ea83f52ba9df2487dda1f9b\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8896984ea83f52ba9df2487dda1f9b\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a69f5246a1a71e421f05951a759a13a\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a69f5246a1a71e421f05951a759a13a\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85171e4206c16718d7c8d5bf43fe825d\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85171e4206c16718d7c8d5bf43fe825d\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cd3daaaa7b2a32f64013d0258d8a30a\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cd3daaaa7b2a32f64013d0258d8a30a\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e754f461ad8f7a511fa7fc6c4385c693\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e754f461ad8f7a511fa7fc6c4385c693\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\37aef1d4a50fb91f5b565408d177e4e2\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\37aef1d4a50fb91f5b565408d177e4e2\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\499cb625e195031490d2b230fd99c673\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\499cb625e195031490d2b230fd99c673\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\306dbeeedef5ec3722ac3802108b58d4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\306dbeeedef5ec3722ac3802108b58d4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9e631c8d1098161560685b7269fb152\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9e631c8d1098161560685b7269fb152\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32b6106b20c62ce5414ad176bfa008e5\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32b6106b20c62ce5414ad176bfa008e5\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6edc257433ad7f57ca34fc395a7be0d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6edc257433ad7f57ca34fc395a7be0d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\69dd2d7e43f73ac56303f4f8edd67cfe\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\69dd2d7e43f73ac56303f4f8edd67cfe\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1225ad1152007df66d50dcffcbe26e25\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1225ad1152007df66d50dcffcbe26e25\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ac64f4a5c821ae5d20223efeb7e5866\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ac64f4a5c821ae5d20223efeb7e5866\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9a938c84971bf6579aa7fdf761e25d5\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9a938c84971bf6579aa7fdf761e25d5\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\368884b31e1d49d62cfc67bcb3df93c5\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\368884b31e1d49d62cfc67bcb3df93c5\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a67d276a67a1709c240307a5ca7afdb8\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a67d276a67a1709c240307a5ca7afdb8\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5655aeb3e3e3070e27621041d463bbe9\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5655aeb3e3e3070e27621041d463bbe9\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f1b8466df86c6c8ca60bf674e7b73d7\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f1b8466df86c6c8ca60bf674e7b73d7\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\561848234033d56cb459dd7112058163\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\561848234033d56cb459dd7112058163\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f61f6209fc0ff581f93622a9402fd8ef\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f61f6209fc0ff581f93622a9402fd8ef\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\3109e90bbcb133a6123d6a171e482e61\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\3109e90bbcb133a6123d6a171e482e61\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7a10baaa6db82e55aa7059558ca8c00\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7a10baaa6db82e55aa7059558ca8c00\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c436e683a15c443b97ae35b9b9f4740\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c436e683a15c443b97ae35b9b9f4740\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc60d967e3e96a9500d84b22b153d157\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc60d967e3e96a9500d84b22b153d157\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a90a4a7ada7d52b4c6f88de75cc10b3\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a90a4a7ada7d52b4c6f88de75cc10b3\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e4d5a7b637c696b107f8ca7a1e781c7\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e4d5a7b637c696b107f8ca7a1e781c7\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\015561b9d3a14a2b0d3725e010e34d2f\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\015561b9d3a14a2b0d3725e010e34d2f\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\872671f11bcf03422d04045da95839b0\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\872671f11bcf03422d04045da95839b0\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d714ce27bb382dd5411af92c5af9fa71\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d714ce27bb382dd5411af92c5af9fa71\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd811a106770e2ea84bb91143af278b4\transformed\lifecycle-viewmodel-2.9.2\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd811a106770e2ea84bb91143af278b4\transformed\lifecycle-viewmodel-2.9.2\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\54c8e97fceebf05a01c2f3ccec507e1e\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\54c8e97fceebf05a01c2f3ccec507e1e\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d22afa2cf35df91f286953a0d38f327c\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d22afa2cf35df91f286953a0d38f327c\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\82acd443bb8e6b620978dbc588e9d803\transformed\lifecycle-livedata-core-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\82acd443bb8e6b620978dbc588e9d803\transformed\lifecycle-livedata-core-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbb2718579edd88a786d10712a0c8e9f\transformed\lifecycle-livedata-core-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbb2718579edd88a786d10712a0c8e9f\transformed\lifecycle-livedata-core-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9aa874683dd79c9062fac97bf4b6c412\transformed\savedstate-ktx-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9aa874683dd79c9062fac97bf4b6c412\transformed\savedstate-ktx-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ed89daf239d020270328bf0b2917631\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ed89daf239d020270328bf0b2917631\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\750ad70ca7d2f2aadf98cdad3b4c7def\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\750ad70ca7d2f2aadf98cdad3b4c7def\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7edce7b8ce0e805ee7581ac75045450a\transformed\lifecycle-viewmodel-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7edce7b8ce0e805ee7581ac75045450a\transformed\lifecycle-viewmodel-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\13aec054922c6b865e26b9010d4084aa\transformed\lifecycle-livedata-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\13aec054922c6b865e26b9010d4084aa\transformed\lifecycle-livedata-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b88ce6832dcb1a9564fbc158fb43c8a3\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b88ce6832dcb1a9564fbc158fb43c8a3\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e6de996d8b54044ba9ed8de4a509bd7\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e6de996d8b54044ba9ed8de4a509bd7\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2b3656dd21bf3a3769f9616474f9651\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2b3656dd21bf3a3769f9616474f9651\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ebcffb00f1687fc3e2fd5e4a23fcae64\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ebcffb00f1687fc3e2fd5e4a23fcae64\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb5978be55e4593326b90344069d1b2d\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb5978be55e4593326b90344069d1b2d\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c756c9cce9b2c5b631b4841f4f3a425a\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c756c9cce9b2c5b631b4841f4f3a425a\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\464fb997f86c74cbb89077e01e66c511\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\464fb997f86c74cbb89077e01e66c511\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a70232a586e72f145ee1e22636868bba\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a70232a586e72f145ee1e22636868bba\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2bbbf4b431026cb8c4d172599690ff63\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2bbbf4b431026cb8c4d172599690ff63\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c19c93bf4b3ae41606569ce25471910a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c19c93bf4b3ae41606569ce25471910a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2ae42fb96d605594219ee072ea3d24b\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2ae42fb96d605594219ee072ea3d24b\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aec2f011c027afa72a7ad78ce4640a13\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aec2f011c027afa72a7ad78ce4640a13\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2308300ca59644f38669bf11db853be\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2308300ca59644f38669bf11db853be\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3dd9e70c81c9ac7c3c96a466ada66e88\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3dd9e70c81c9ac7c3c96a466ada66e88\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\91a0e68982f831fac00972bf1238b759\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\91a0e68982f831fac00972bf1238b759\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\a161a899397881ed0a3da01ece1dbb8f\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\a161a899397881ed0a3da01ece1dbb8f\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08676af66e63b63b9c97decd248ae0ed\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08676af66e63b63b9c97decd248ae0ed\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f9fffeca9b4e75d900913b18ac2df78\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f9fffeca9b4e75d900913b18ac2df78\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\748dcec305cccd386b1adf80082d9293\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\748dcec305cccd386b1adf80082d9293\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c101beef2568fba5283b14c16a1e818\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c101beef2568fba5283b14c16a1e818\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d6d2a8a7272352c082e2050a2b41847\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d6d2a8a7272352c082e2050a2b41847\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\59782c46e92902f9e632069eb2572923\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\59782c46e92902f9e632069eb2572923\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\268192141123e01386248e6368401d3e\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\268192141123e01386248e6368401d3e\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb891f82f60120b0609f05d68d17a84f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb891f82f60120b0609f05d68d17a84f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3b6530e7271de0fec1178f5d19b403e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3b6530e7271de0fec1178f5d19b403e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b30944cffca17a947fd6a78050968f33\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b30944cffca17a947fd6a78050968f33\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from D:\tcs\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\tcs\app\src\main\AndroidManifest.xml
queries
ADDED from [androidx.camera:camera-extensions:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\73eda55041be236a16720a4a764f5ab1\transformed\camera-extensions-1.3.4\AndroidManifest.xml:22:5-26:15
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\73eda55041be236a16720a4a764f5ab1\transformed\camera-extensions-1.3.4\AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\73eda55041be236a16720a4a764f5ab1\transformed\camera-extensions-1.3.4\AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\73eda55041be236a16720a4a764f5ab1\transformed\camera-extensions-1.3.4\AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\73eda55041be236a16720a4a764f5ab1\transformed\camera-extensions-1.3.4\AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\73eda55041be236a16720a4a764f5ab1\transformed\camera-extensions-1.3.4\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\73eda55041be236a16720a4a764f5ab1\transformed\camera-extensions-1.3.4\AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d0c1c396aebaa0e12210312a4aec0ec\transformed\camera-core-1.3.4\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d0c1c396aebaa0e12210312a4aec0ec\transformed\camera-core-1.3.4\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:31:17-103
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\368884b31e1d49d62cfc67bcb3df93c5\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\368884b31e1d49d62cfc67bcb3df93c5\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\368884b31e1d49d62cfc67bcb3df93c5\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\59782c46e92902f9e632069eb2572923\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\59782c46e92902f9e632069eb2572923\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.example.tcs.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.tcs.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:30:17-78
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c756c9cce9b2c5b631b4841f4f3a425a\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c756c9cce9b2c5b631b4841f4f3a425a\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c756c9cce9b2c5b631b4841f4f3a425a\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
