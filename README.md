# 多功能相机应用

一个功能丰富的Android相机应用，基于Jetpack Compose和CameraX构建，提供专业级的拍照和录像体验。

## 🌟 主要功能

### 📸 基础拍摄功能
- **高质量拍照**: 支持多种分辨率和宽高比
- **视频录制**: 支持HD、Full HD、4K录制
- **前后摄像头切换**: 一键切换前后摄像头
- **闪光灯控制**: 自动、开启、关闭三种模式
- **实时预览**: 流畅的相机预览体验

### 🎛️ 专业模式
- **手动ISO控制**: 100-3200范围调节
- **曝光补偿**: -6到+6档调节
- **白平衡调节**: 自动、日光、阴天、钨丝灯等模式
- **对焦模式**: 自动、手动、无限远对焦
- **快门速度控制**: 专业级快门控制

### 🎨 拍摄模式
- **人像模式**: 优化人像拍摄效果
- **夜景模式**: 低光环境拍摄优化
- **全景模式**: 宽幅全景拍摄
- **延时摄影**: 可自定义间隔和时长
- **慢动作**: 高帧率慢动作录制

### 🖼️ 图片管理
- **相册浏览**: 网格式照片和视频浏览
- **图片查看**: 支持缩放、旋转、平移
- **基础编辑**: 滤镜、裁剪、调整功能
- **分享功能**: 快速分享到其他应用
- **删除管理**: 安全删除不需要的文件

### ⚙️ 高级设置
- **图像质量**: 低、中、高、超高四档质量
- **视频质量**: 480p到4K多种选择
- **网格线**: 九宫格构图辅助
- **定时器**: 3秒、5秒、10秒延时拍摄
- **HDR**: 高动态范围成像
- **防抖**: 图像稳定化技术
- **滤镜**: 多种艺术滤镜效果

## 🛠️ 技术架构

### 核心技术栈
- **Jetpack Compose**: 现代化UI框架
- **CameraX**: Google官方相机库
- **Kotlin**: 100% Kotlin开发
- **MVVM架构**: 清晰的架构模式
- **Coroutines**: 异步编程
- **StateFlow**: 响应式状态管理

### 主要依赖
```kotlin
// CameraX
implementation("androidx.camera:camera-core:1.3.4")
implementation("androidx.camera:camera-camera2:1.3.4")
implementation("androidx.camera:camera-lifecycle:1.3.4")
implementation("androidx.camera:camera-video:1.3.4")
implementation("androidx.camera:camera-view:1.3.4")

// Compose
implementation("androidx.compose.ui:ui")
implementation("androidx.compose.material3:material3")
implementation("androidx.navigation:navigation-compose:2.7.7")

// 权限处理
implementation("com.google.accompanist:accompanist-permissions:0.32.0")

// 图像处理
implementation("com.github.bumptech.glide:compose:1.0.0-beta01")
```

## 📱 系统要求

- **最低Android版本**: Android 10 (API 29)
- **目标Android版本**: Android 14 (API 36)
- **必需硬件**: 后置摄像头
- **推荐硬件**: 前置摄像头、闪光灯、自动对焦

## 🔐 权限说明

应用需要以下权限才能正常工作：

- **相机权限**: 用于拍照和录像
- **录音权限**: 用于录制视频音频
- **存储权限**: 用于保存和读取照片视频
- **位置权限**: 用于照片地理位置标记（可选）

## 🚀 安装和运行

### 开发环境要求
- Android Studio Hedgehog | 2023.1.1 或更高版本
- JDK 11 或更高版本
- Android SDK 34 或更高版本

### 构建步骤
1. 克隆项目到本地
```bash
git clone [项目地址]
cd tcs
```

2. 使用Android Studio打开项目

3. 等待Gradle同步完成

4. 连接Android设备或启动模拟器

5. 点击运行按钮或使用命令行：
```bash
./gradlew installDebug
```

## 📂 项目结构

```
app/src/main/java/com/example/tcs/
├── camera/                 # 相机核心功能
│   └── CameraController.kt
├── data/                   # 数据模型
│   └── CameraSettings.kt
├── ui/                     # UI界面
│   ├── camera/            # 相机界面
│   ├── gallery/           # 相册界面
│   ├── settings/          # 设置界面
│   ├── viewer/            # 图片查看器
│   ├── professional/      # 专业模式
│   ├── timelapse/         # 延时摄影
│   └── theme/             # 主题配置
├── utils/                  # 工具类
│   ├── PermissionUtils.kt
│   ├── MediaUtils.kt
│   └── ErrorHandler.kt
├── viewmodel/             # ViewModel
│   ├── CameraViewModel.kt
│   └── GalleryViewModel.kt
└── MainActivity.kt        # 主Activity
```

## 🎯 使用指南

### 基础拍摄
1. 打开应用，授予必要权限
2. 对准拍摄目标
3. 点击白色圆形按钮拍照
4. 长按录制视频

### 切换模式
- 在预览界面底部滑动选择不同拍摄模式
- 点击设置按钮进入详细设置

### 专业模式
1. 在设置中开启专业模式
2. 手动调节ISO、曝光、白平衡等参数
3. 获得更精确的拍摄控制

### 延时摄影
1. 选择延时摄影模式
2. 设置拍摄间隔和总时长
3. 点击开始按钮开始录制

## 🔧 自定义和扩展

### 添加新滤镜
在`CameraFilter`枚举中添加新的滤镜类型，并在相应的处理逻辑中实现效果。

### 自定义拍摄模式
1. 在`CaptureMode`枚举中添加新模式
2. 在`CameraController`中实现对应逻辑
3. 在UI中添加模式选择

### 扩展设置选项
在`CameraSettings`数据类中添加新的设置项，并在设置界面中添加对应的UI控件。

## 🐛 已知问题

- 某些设备上的前置相机可能不支持所有功能
- 延时摄影在低端设备上可能影响性能
- 4K录制需要足够的存储空间

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至：[<EMAIL>]

---

**享受拍摄的乐趣！📸**
