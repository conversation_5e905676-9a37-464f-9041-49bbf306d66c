1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.tcs"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
9        android:targetSdkVersion="36" />
10
11    <!-- Camera permissions -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->D:\tcs\app\src\main\AndroidManifest.xml:6:5-65
12-->D:\tcs\app\src\main\AndroidManifest.xml:6:22-62
13    <uses-permission android:name="android.permission.RECORD_AUDIO" />
13-->D:\tcs\app\src\main\AndroidManifest.xml:7:5-71
13-->D:\tcs\app\src\main\AndroidManifest.xml:7:22-68
14
15    <!-- Storage permissions -->
16    <uses-permission
16-->D:\tcs\app\src\main\AndroidManifest.xml:10:5-11:38
17        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
17-->D:\tcs\app\src\main\AndroidManifest.xml:10:22-78
18        android:maxSdkVersion="28" />
18-->D:\tcs\app\src\main\AndroidManifest.xml:11:9-35
19    <uses-permission
19-->D:\tcs\app\src\main\AndroidManifest.xml:12:5-13:38
20        android:name="android.permission.READ_EXTERNAL_STORAGE"
20-->D:\tcs\app\src\main\AndroidManifest.xml:12:22-77
21        android:maxSdkVersion="32" />
21-->D:\tcs\app\src\main\AndroidManifest.xml:13:9-35
22    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
22-->D:\tcs\app\src\main\AndroidManifest.xml:14:5-76
22-->D:\tcs\app\src\main\AndroidManifest.xml:14:22-73
23    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
23-->D:\tcs\app\src\main\AndroidManifest.xml:15:5-75
23-->D:\tcs\app\src\main\AndroidManifest.xml:15:22-72
24
25    <!-- Camera features -->
26    <uses-feature
26-->D:\tcs\app\src\main\AndroidManifest.xml:18:5-20:35
27        android:name="android.hardware.camera"
27-->D:\tcs\app\src\main\AndroidManifest.xml:19:9-47
28        android:required="true" />
28-->D:\tcs\app\src\main\AndroidManifest.xml:20:9-32
29    <uses-feature
29-->D:\tcs\app\src\main\AndroidManifest.xml:21:5-23:36
30        android:name="android.hardware.camera.autofocus"
30-->D:\tcs\app\src\main\AndroidManifest.xml:22:9-57
31        android:required="false" />
31-->D:\tcs\app\src\main\AndroidManifest.xml:23:9-33
32    <uses-feature
32-->D:\tcs\app\src\main\AndroidManifest.xml:24:5-26:36
33        android:name="android.hardware.camera.flash"
33-->D:\tcs\app\src\main\AndroidManifest.xml:25:9-53
34        android:required="false" />
34-->D:\tcs\app\src\main\AndroidManifest.xml:26:9-33
35    <uses-feature
35-->D:\tcs\app\src\main\AndroidManifest.xml:27:5-29:36
36        android:name="android.hardware.camera.front"
36-->D:\tcs\app\src\main\AndroidManifest.xml:28:9-53
37        android:required="false" />
37-->D:\tcs\app\src\main\AndroidManifest.xml:29:9-33
38
39    <queries>
39-->[androidx.camera:camera-extensions:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\73eda55041be236a16720a4a764f5ab1\transformed\camera-extensions-1.3.4\AndroidManifest.xml:22:5-26:15
40        <intent>
40-->[androidx.camera:camera-extensions:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\73eda55041be236a16720a4a764f5ab1\transformed\camera-extensions-1.3.4\AndroidManifest.xml:23:9-25:18
41            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
41-->[androidx.camera:camera-extensions:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\73eda55041be236a16720a4a764f5ab1\transformed\camera-extensions-1.3.4\AndroidManifest.xml:24:13-86
41-->[androidx.camera:camera-extensions:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\73eda55041be236a16720a4a764f5ab1\transformed\camera-extensions-1.3.4\AndroidManifest.xml:24:21-83
42        </intent>
43    </queries>
44
45    <permission
45-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
46        android:name="com.example.tcs.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
46-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
47        android:protectionLevel="signature" />
47-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
48
49    <uses-permission android:name="com.example.tcs.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
49-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
49-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
50
51    <application
51-->D:\tcs\app\src\main\AndroidManifest.xml:31:5-63:19
52        android:allowBackup="true"
52-->D:\tcs\app\src\main\AndroidManifest.xml:32:9-35
53        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
53-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
54        android:dataExtractionRules="@xml/data_extraction_rules"
54-->D:\tcs\app\src\main\AndroidManifest.xml:33:9-65
55        android:debuggable="true"
56        android:extractNativeLibs="false"
57        android:fullBackupContent="@xml/backup_rules"
57-->D:\tcs\app\src\main\AndroidManifest.xml:34:9-54
58        android:icon="@mipmap/ic_launcher"
58-->D:\tcs\app\src\main\AndroidManifest.xml:35:9-43
59        android:label="@string/app_name"
59-->D:\tcs\app\src\main\AndroidManifest.xml:36:9-41
60        android:roundIcon="@mipmap/ic_launcher_round"
60-->D:\tcs\app\src\main\AndroidManifest.xml:37:9-54
61        android:supportsRtl="true"
61-->D:\tcs\app\src\main\AndroidManifest.xml:38:9-35
62        android:theme="@style/Theme.Tcs" >
62-->D:\tcs\app\src\main\AndroidManifest.xml:39:9-41
63        <activity
63-->D:\tcs\app\src\main\AndroidManifest.xml:40:9-51:20
64            android:name="com.example.tcs.MainActivity"
64-->D:\tcs\app\src\main\AndroidManifest.xml:41:13-41
65            android:exported="true"
65-->D:\tcs\app\src\main\AndroidManifest.xml:42:13-36
66            android:label="@string/app_name"
66-->D:\tcs\app\src\main\AndroidManifest.xml:43:13-45
67            android:screenOrientation="portrait"
67-->D:\tcs\app\src\main\AndroidManifest.xml:45:13-49
68            android:theme="@style/Theme.Tcs" >
68-->D:\tcs\app\src\main\AndroidManifest.xml:44:13-45
69            <intent-filter>
69-->D:\tcs\app\src\main\AndroidManifest.xml:46:13-50:29
70                <action android:name="android.intent.action.MAIN" />
70-->D:\tcs\app\src\main\AndroidManifest.xml:47:17-69
70-->D:\tcs\app\src\main\AndroidManifest.xml:47:25-66
71
72                <category android:name="android.intent.category.LAUNCHER" />
72-->D:\tcs\app\src\main\AndroidManifest.xml:49:17-77
72-->D:\tcs\app\src\main\AndroidManifest.xml:49:27-74
73            </intent-filter>
74        </activity>
75
76        <!-- File provider for sharing images -->
77        <provider
78            android:name="androidx.core.content.FileProvider"
78-->D:\tcs\app\src\main\AndroidManifest.xml:55:13-62
79            android:authorities="com.example.tcs.fileprovider"
79-->D:\tcs\app\src\main\AndroidManifest.xml:56:13-64
80            android:exported="false"
80-->D:\tcs\app\src\main\AndroidManifest.xml:57:13-37
81            android:grantUriPermissions="true" >
81-->D:\tcs\app\src\main\AndroidManifest.xml:58:13-47
82            <meta-data
82-->D:\tcs\app\src\main\AndroidManifest.xml:59:13-61:54
83                android:name="android.support.FILE_PROVIDER_PATHS"
83-->D:\tcs\app\src\main\AndroidManifest.xml:60:17-67
84                android:resource="@xml/file_paths" />
84-->D:\tcs\app\src\main\AndroidManifest.xml:61:17-51
85        </provider>
86
87        <uses-library
87-->[androidx.camera:camera-extensions:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\73eda55041be236a16720a4a764f5ab1\transformed\camera-extensions-1.3.4\AndroidManifest.xml:29:9-31:40
88            android:name="androidx.camera.extensions.impl"
88-->[androidx.camera:camera-extensions:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\73eda55041be236a16720a4a764f5ab1\transformed\camera-extensions-1.3.4\AndroidManifest.xml:30:13-59
89            android:required="false" />
89-->[androidx.camera:camera-extensions:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\73eda55041be236a16720a4a764f5ab1\transformed\camera-extensions-1.3.4\AndroidManifest.xml:31:13-37
90
91        <service
91-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:24:9-33:19
92            android:name="androidx.camera.core.impl.MetadataHolderService"
92-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:25:13-75
93            android:enabled="false"
93-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:26:13-36
94            android:exported="false" >
94-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:27:13-37
95            <meta-data
95-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:30:13-32:89
96                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
96-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:31:17-103
97                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
97-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c89f2addd7bca8bcb8cedaed4e86877\transformed\camera-camera2-1.3.4\AndroidManifest.xml:32:17-86
98        </service>
99
100        <activity
100-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\368884b31e1d49d62cfc67bcb3df93c5\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
101            android:name="androidx.compose.ui.tooling.PreviewActivity"
101-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\368884b31e1d49d62cfc67bcb3df93c5\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
102            android:exported="true" />
102-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\368884b31e1d49d62cfc67bcb3df93c5\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
103
104        <provider
104-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
105            android:name="androidx.startup.InitializationProvider"
105-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
106            android:authorities="com.example.tcs.androidx-startup"
106-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
107            android:exported="false" >
107-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
108            <meta-data
108-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
109                android:name="androidx.emoji2.text.EmojiCompatInitializer"
109-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
110                android:value="androidx.startup" />
110-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
111            <meta-data
111-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:29:13-31:52
112                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
112-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:30:17-78
113                android:value="androidx.startup" />
113-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:31:17-49
114            <meta-data
114-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
115                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
115-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
116                android:value="androidx.startup" />
116-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
117        </provider>
118
119        <activity
119-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c756c9cce9b2c5b631b4841f4f3a425a\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
120            android:name="androidx.activity.ComponentActivity"
120-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c756c9cce9b2c5b631b4841f4f3a425a\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
121            android:exported="true" />
121-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c756c9cce9b2c5b631b4841f4f3a425a\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
122
123        <receiver
123-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
124            android:name="androidx.profileinstaller.ProfileInstallReceiver"
124-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
125            android:directBootAware="false"
125-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
126            android:enabled="true"
126-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
127            android:exported="true"
127-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
128            android:permission="android.permission.DUMP" >
128-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
129            <intent-filter>
129-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
130                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
130-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
130-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
131            </intent-filter>
132            <intent-filter>
132-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
133                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
133-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
133-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
134            </intent-filter>
135            <intent-filter>
135-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
136                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
136-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
136-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
137            </intent-filter>
138            <intent-filter>
138-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
139                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
139-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
139-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
140            </intent-filter>
141        </receiver>
142    </application>
143
144</manifest>
