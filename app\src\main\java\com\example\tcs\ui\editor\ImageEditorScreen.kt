package com.example.tcs.ui.editor

import android.content.Context
import android.graphics.*
import android.net.Uri
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.AsyncImage
import com.example.tcs.viewmodel.ImageEditorViewModel

/**
 * 图片编辑界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ImageEditorScreen(
    imageUri: Uri,
    onNavigateBack: () -> Unit,
    onSaveImage: (Uri) -> Unit,
    viewModel: ImageEditorViewModel = viewModel()
) {
    val context = LocalContext.current
    val editState by viewModel.editState.collectAsState()
    
    LaunchedEffect(imageUri) {
        viewModel.loadImage(context, imageUri)
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("编辑图片") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    TextButton(
                        onClick = {
                            viewModel.saveEditedImage(context) { savedUri ->
                                savedUri?.let { onSaveImage(it) }
                            }
                        }
                    ) {
                        Text("保存")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 图片预览区域
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .background(Color.Black)
            ) {
                AsyncImage(
                    model = editState.editedBitmap ?: imageUri,
                    contentDescription = null,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Fit
                )
            }
            
            // 编辑工具栏
            EditToolbar(
                currentTool = editState.currentTool,
                onToolSelected = { viewModel.selectTool(it) }
            )
            
            // 工具参数控制
            when (editState.currentTool) {
                EditTool.BRIGHTNESS -> {
                    BrightnessControl(
                        value = editState.brightness,
                        onValueChange = { viewModel.adjustBrightness(it) }
                    )
                }
                EditTool.CONTRAST -> {
                    ContrastControl(
                        value = editState.contrast,
                        onValueChange = { viewModel.adjustContrast(it) }
                    )
                }
                EditTool.SATURATION -> {
                    SaturationControl(
                        value = editState.saturation,
                        onValueChange = { viewModel.adjustSaturation(it) }
                    )
                }
                EditTool.FILTER -> {
                    FilterControl(
                        selectedFilter = editState.selectedFilter,
                        onFilterSelected = { viewModel.applyFilter(it) }
                    )
                }
                EditTool.ROTATE -> {
                    RotateControl(
                        onRotateLeft = { viewModel.rotateImage(-90f) },
                        onRotateRight = { viewModel.rotateImage(90f) },
                        onFlipHorizontal = { viewModel.flipImage(horizontal = true) },
                        onFlipVertical = { viewModel.flipImage(horizontal = false) }
                    )
                }
                else -> {
                    // 默认工具栏
                    Spacer(modifier = Modifier.height(80.dp))
                }
            }
        }
    }
}

/**
 * 编辑工具栏
 */
@Composable
private fun EditToolbar(
    currentTool: EditTool,
    onToolSelected: (EditTool) -> Unit
) {
    LazyRow(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        items(EditTool.values()) { tool ->
            ToolButton(
                tool = tool,
                isSelected = currentTool == tool,
                onClick = { onToolSelected(tool) }
            )
        }
    }
}

/**
 * 工具按钮
 */
@Composable
private fun ToolButton(
    tool: EditTool,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.width(60.dp)
    ) {
        IconButton(
            onClick = onClick,
            modifier = Modifier
                .size(48.dp)
                .background(
                    if (isSelected) MaterialTheme.colorScheme.primary else Color.Transparent,
                    CircleShape
                )
        ) {
            Icon(
                imageVector = tool.icon,
                contentDescription = tool.displayName,
                tint = if (isSelected) Color.White else MaterialTheme.colorScheme.onSurface
            )
        }
        Text(
            text = tool.displayName,
            style = MaterialTheme.typography.labelSmall,
            color = if (isSelected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface
        )
    }
}

/**
 * 亮度控制
 */
@Composable
private fun BrightnessControl(
    value: Float,
    onValueChange: (Float) -> Unit
) {
    ParameterControl(
        title = "亮度",
        value = value,
        valueRange = -100f..100f,
        onValueChange = onValueChange
    )
}

/**
 * 对比度控制
 */
@Composable
private fun ContrastControl(
    value: Float,
    onValueChange: (Float) -> Unit
) {
    ParameterControl(
        title = "对比度",
        value = value,
        valueRange = -100f..100f,
        onValueChange = onValueChange
    )
}

/**
 * 饱和度控制
 */
@Composable
private fun SaturationControl(
    value: Float,
    onValueChange: (Float) -> Unit
) {
    ParameterControl(
        title = "饱和度",
        value = value,
        valueRange = -100f..100f,
        onValueChange = onValueChange
    )
}

/**
 * 参数控制组件
 */
@Composable
private fun ParameterControl(
    title: String,
    value: Float,
    valueRange: ClosedFloatingPointRange<Float>,
    onValueChange: (Float) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "${value.toInt()}",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Slider(
                value = value,
                onValueChange = onValueChange,
                valueRange = valueRange,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 滤镜控制
 */
@Composable
private fun FilterControl(
    selectedFilter: ImageFilter,
    onFilterSelected: (ImageFilter) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "滤镜",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(12.dp))

            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(ImageFilter.values()) { filter ->
                    FilterChip(
                        onClick = { onFilterSelected(filter) },
                        label = { Text(filter.displayName) },
                        selected = selectedFilter == filter
                    )
                }
            }
        }
    }
}

/**
 * 旋转控制
 */
@Composable
private fun RotateControl(
    onRotateLeft: () -> Unit,
    onRotateRight: () -> Unit,
    onFlipHorizontal: () -> Unit,
    onFlipVertical: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "旋转和翻转",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                IconButton(
                    onClick = onRotateLeft,
                    modifier = Modifier
                        .background(
                            MaterialTheme.colorScheme.primaryContainer,
                            RoundedCornerShape(8.dp)
                        )
                ) {
                    Icon(
                        imageVector = Icons.Default.RotateLeft,
                        contentDescription = "向左旋转"
                    )
                }

                IconButton(
                    onClick = onRotateRight,
                    modifier = Modifier
                        .background(
                            MaterialTheme.colorScheme.primaryContainer,
                            RoundedCornerShape(8.dp)
                        )
                ) {
                    Icon(
                        imageVector = Icons.Default.RotateRight,
                        contentDescription = "向右旋转"
                    )
                }

                IconButton(
                    onClick = onFlipHorizontal,
                    modifier = Modifier
                        .background(
                            MaterialTheme.colorScheme.primaryContainer,
                            RoundedCornerShape(8.dp)
                        )
                ) {
                    Icon(
                        imageVector = Icons.Default.Flip,
                        contentDescription = "水平翻转"
                    )
                }

                IconButton(
                    onClick = onFlipVertical,
                    modifier = Modifier
                        .background(
                            MaterialTheme.colorScheme.primaryContainer,
                            RoundedCornerShape(8.dp)
                        )
                ) {
                    Icon(
                        imageVector = Icons.Default.FlipCameraAndroid,
                        contentDescription = "垂直翻转"
                    )
                }
            }
        }
    }
}

/**
 * 编辑工具枚举
 */
enum class EditTool(val displayName: String, val icon: androidx.compose.ui.graphics.vector.ImageVector) {
    BRIGHTNESS("亮度", Icons.Default.Brightness6),
    CONTRAST("对比度", Icons.Default.Contrast),
    SATURATION("饱和度", Icons.Default.Colorize),
    FILTER("滤镜", Icons.Default.FilterVintage),
    ROTATE("旋转", Icons.Default.RotateRight),
    CROP("裁剪", Icons.Default.Crop)
}

/**
 * 图片滤镜枚举
 */
enum class ImageFilter(val displayName: String) {
    NONE("原图"),
    GRAYSCALE("黑白"),
    SEPIA("复古"),
    VINTAGE("怀旧"),
    WARM("暖色"),
    COOL("冷色"),
    VIVID("鲜艳"),
    DRAMATIC("戏剧")
}
