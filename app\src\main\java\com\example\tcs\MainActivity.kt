package com.example.tcs

import android.net.Uri
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.runtime.*
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.tcs.ui.camera.CameraPreviewScreen
import com.example.tcs.ui.gallery.GalleryScreen
import com.example.tcs.ui.settings.CameraSettingsScreen
import com.example.tcs.ui.theme.TcsTheme
import com.example.tcs.ui.viewer.ImageViewerScreen

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            TcsTheme {
                CameraApp()
            }
        }
    }
}

@Composable
fun CameraApp() {
    val navController = rememberNavController()
    var selectedImageUri by remember { mutableStateOf<Uri?>(null) }

    NavHost(
        navController = navController,
        startDestination = "camera"
    ) {
        // 相机预览界面
        composable("camera") {
            CameraPreviewScreen(
                onNavigateToSettings = {
                    navController.navigate("settings")
                },
                onNavigateToGallery = {
                    navController.navigate("gallery")
                }
            )
        }

        // 相机设置界面
        composable("settings") {
            CameraSettingsScreen(
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }

        // 相册界面
        composable("gallery") {
            GalleryScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onImageSelected = { uri ->
                    selectedImageUri = uri
                    navController.navigate("viewer")
                }
            )
        }

        // 图片查看器
        composable("viewer") {
            selectedImageUri?.let { uri ->
                ImageViewerScreen(
                    imageUri = uri,
                    onNavigateBack = {
                        navController.popBackStack()
                    },
                    onEdit = {
                        // TODO: 实现图片编辑功能
                    },
                    onShare = {
                        // TODO: 实现分享功能
                    },
                    onDelete = {
                        // TODO: 实现删除功能
                        navController.popBackStack()
                    }
                )
            }
        }
    }
}