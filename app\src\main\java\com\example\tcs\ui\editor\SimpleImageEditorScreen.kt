package com.example.tcs.ui.editor

import android.content.Context
import android.graphics.*
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.example.tcs.utils.MediaUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.InputStream

/**
 * 简化版图片编辑界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SimpleImageEditorScreen(
    imageUri: Uri,
    onNavigateBack: () -> Unit,
    onSaveImage: (Uri) -> Unit
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    var currentFilter by remember { mutableStateOf(SimpleFilter.NONE) }
    var brightness by remember { mutableStateOf(0f) }
    var contrast by remember { mutableStateOf(0f) }
    var isProcessing by remember { mutableStateOf(false) }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("编辑图片") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    TextButton(
                        onClick = {
                            scope.launch {
                                isProcessing = true
                                try {
                                    val editedUri = processAndSaveImage(
                                        context, imageUri, currentFilter, brightness, contrast
                                    )
                                    editedUri?.let { onSaveImage(it) }
                                } finally {
                                    isProcessing = false
                                }
                            }
                        },
                        enabled = !isProcessing
                    ) {
                        if (isProcessing) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                strokeWidth = 2.dp
                            )
                        } else {
                            Text("保存")
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 图片预览
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .background(Color.Black)
            ) {
                AsyncImage(
                    model = imageUri,
                    contentDescription = null,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Fit
                )
            }
            
            // 滤镜选择
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "滤镜",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    LazyRow(
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        items(SimpleFilter.values()) { filter ->
                            FilterChip(
                                onClick = { currentFilter = filter },
                                label = { Text(filter.displayName) },
                                selected = currentFilter == filter
                            )
                        }
                    }
                }
            }
            
            // 亮度调整
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "亮度",
                            style = MaterialTheme.typography.titleMedium
                        )
                        Text(
                            text = "${brightness.toInt()}",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                    
                    Slider(
                        value = brightness,
                        onValueChange = { brightness = it },
                        valueRange = -100f..100f,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
            
            // 对比度调整
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "对比度",
                            style = MaterialTheme.typography.titleMedium
                        )
                        Text(
                            text = "${contrast.toInt()}",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                    
                    Slider(
                        value = contrast,
                        onValueChange = { contrast = it },
                        valueRange = -100f..100f,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

/**
 * 处理并保存图片
 */
private suspend fun processAndSaveImage(
    context: Context,
    imageUri: Uri,
    filter: SimpleFilter,
    brightness: Float,
    contrast: Float
): Uri? = withContext(Dispatchers.IO) {
    try {
        // 加载原始图片
        val inputStream: InputStream? = context.contentResolver.openInputStream(imageUri)
        val originalBitmap = BitmapFactory.decodeStream(inputStream)
        inputStream?.close()
        
        if (originalBitmap == null) return@withContext null
        
        // 应用编辑效果
        var editedBitmap = originalBitmap.copy(Bitmap.Config.ARGB_8888, true)
        
        // 应用亮度和对比度
        if (brightness != 0f || contrast != 0f) {
            editedBitmap = applyBrightnessContrast(editedBitmap, brightness, contrast)
        }
        
        // 应用滤镜
        editedBitmap = applySimpleFilter(editedBitmap, filter)
        
        // 保存图片
        MediaUtils.saveImageToGallery(context, editedBitmap)
    } catch (e: Exception) {
        null
    }
}

/**
 * 应用亮度和对比度
 */
private fun applyBrightnessContrast(bitmap: Bitmap, brightness: Float, contrast: Float): Bitmap {
    val colorMatrix = ColorMatrix()
    
    // 亮度调整
    val brightnessValue = brightness / 100f * 255f
    colorMatrix.set(floatArrayOf(
        1f, 0f, 0f, 0f, brightnessValue,
        0f, 1f, 0f, 0f, brightnessValue,
        0f, 0f, 1f, 0f, brightnessValue,
        0f, 0f, 0f, 1f, 0f
    ))
    
    // 对比度调整
    val contrastValue = (contrast + 100f) / 100f
    val contrastMatrix = ColorMatrix(floatArrayOf(
        contrastValue, 0f, 0f, 0f, 0f,
        0f, contrastValue, 0f, 0f, 0f,
        0f, 0f, contrastValue, 0f, 0f,
        0f, 0f, 0f, 1f, 0f
    ))
    colorMatrix.postConcat(contrastMatrix)
    
    val paint = Paint()
    paint.colorFilter = ColorMatrixColorFilter(colorMatrix)
    
    val result = Bitmap.createBitmap(bitmap.width, bitmap.height, Bitmap.Config.ARGB_8888)
    val canvas = Canvas(result)
    canvas.drawBitmap(bitmap, 0f, 0f, paint)
    
    return result
}

/**
 * 应用简单滤镜
 */
private fun applySimpleFilter(bitmap: Bitmap, filter: SimpleFilter): Bitmap {
    return when (filter) {
        SimpleFilter.NONE -> bitmap
        SimpleFilter.GRAYSCALE -> {
            val colorMatrix = ColorMatrix()
            colorMatrix.setSaturation(0f)
            applyColorMatrix(bitmap, colorMatrix)
        }
        SimpleFilter.SEPIA -> {
            val colorMatrix = ColorMatrix(floatArrayOf(
                0.393f, 0.769f, 0.189f, 0f, 0f,
                0.349f, 0.686f, 0.168f, 0f, 0f,
                0.272f, 0.534f, 0.131f, 0f, 0f,
                0f, 0f, 0f, 1f, 0f
            ))
            applyColorMatrix(bitmap, colorMatrix)
        }
        SimpleFilter.WARM -> {
            val colorMatrix = ColorMatrix(floatArrayOf(
                1.2f, 0f, 0f, 0f, 15f,
                0f, 1.1f, 0f, 0f, 10f,
                0f, 0f, 0.9f, 0f, 0f,
                0f, 0f, 0f, 1f, 0f
            ))
            applyColorMatrix(bitmap, colorMatrix)
        }
        SimpleFilter.COOL -> {
            val colorMatrix = ColorMatrix(floatArrayOf(
                0.9f, 0f, 0f, 0f, 0f,
                0f, 1.0f, 0f, 0f, 0f,
                0f, 0f, 1.2f, 0f, 15f,
                0f, 0f, 0f, 1f, 0f
            ))
            applyColorMatrix(bitmap, colorMatrix)
        }
    }
}

/**
 * 应用颜色矩阵
 */
private fun applyColorMatrix(bitmap: Bitmap, colorMatrix: ColorMatrix): Bitmap {
    val paint = Paint()
    paint.colorFilter = ColorMatrixColorFilter(colorMatrix)
    
    val result = Bitmap.createBitmap(bitmap.width, bitmap.height, Bitmap.Config.ARGB_8888)
    val canvas = Canvas(result)
    canvas.drawBitmap(bitmap, 0f, 0f, paint)
    
    return result
}

/**
 * 简单滤镜枚举
 */
enum class SimpleFilter(val displayName: String) {
    NONE("原图"),
    GRAYSCALE("黑白"),
    SEPIA("复古"),
    WARM("暖色"),
    COOL("冷色")
}
