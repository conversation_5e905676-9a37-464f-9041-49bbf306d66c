package com.example.tcs.ui.viewer

import android.net.Uri
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.rememberTransformableState
import androidx.compose.foundation.gestures.transformable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.example.tcs.utils.ShareUtils
import java.io.File
import android.content.Context

/**
 * 图片查看器界面
 */
@Composable
fun ImageViewerScreen(
    imageUri: Uri,
    onNavigateBack: () -> Unit,
    onEdit: () -> Unit,
    onShare: () -> Unit,
    onDelete: () -> Unit
) {
    val context = LocalContext.current
    var showDeleteDialog by remember { mutableStateOf(false) }
    var scale by remember { mutableStateOf(1f) }
    var rotation by remember { mutableStateOf(0f) }
    var offset by remember { mutableStateOf(Offset.Zero) }
    var showControls by remember { mutableStateOf(true) }
    
    val state = rememberTransformableState { zoomChange, offsetChange, rotationChange ->
        scale *= zoomChange
        rotation += rotationChange
        offset += offsetChange
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        // 图片显示
        AsyncImage(
            model = imageUri,
            contentDescription = null,
            modifier = Modifier
                .fillMaxSize()
                .graphicsLayer(
                    scaleX = scale,
                    scaleY = scale,
                    rotationZ = rotation,
                    translationX = offset.x,
                    translationY = offset.y
                )
                .transformable(state = state)
                .clickable {
                    // 点击图片切换控制栏显示状态
                    showControls = !showControls
                },
            contentScale = ContentScale.Fit
        )
        
        // 顶部控制栏
        if (showControls) {
            TopControlBar(
                onNavigateBack = onNavigateBack,
                onShare = {
                    ShareUtils.shareImage(context, imageUri)
                },
                onDelete = {
                    showDeleteDialog = true
                },
                modifier = Modifier.align(Alignment.TopCenter)
            )
        }
        
        // 底部控制栏
        if (showControls) {
            BottomControlBar(
                onEdit = onEdit,
                onResetTransform = {
                    scale = 1f
                    rotation = 0f
                    offset = Offset.Zero
                },
                onToggleControls = { showControls = !showControls },
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
        


        // 删除确认对话框
        if (showDeleteDialog) {
            AlertDialog(
                onDismissRequest = { showDeleteDialog = false },
                title = { Text("删除图片") },
                text = { Text("确定要删除这张图片吗？此操作无法撤销。") },
                confirmButton = {
                    TextButton(
                        onClick = {
                            showDeleteDialog = false
                            deleteImage(context, imageUri)
                            onDelete()
                        }
                    ) {
                        Text("删除")
                    }
                },
                dismissButton = {
                    TextButton(
                        onClick = { showDeleteDialog = false }
                    ) {
                        Text("取消")
                    }
                }
            )
        }
    }
}

/**
 * 删除图片
 */
private fun deleteImage(context: Context, imageUri: Uri) {
    try {
        if (imageUri.scheme == "file") {
            // 删除本地文件
            val file = File(imageUri.path ?: return)
            file.delete()
        } else {
            // 删除MediaStore中的文件
            context.contentResolver.delete(imageUri, null, null)
        }
    } catch (e: Exception) {
        // 处理删除错误
    }
}

/**
 * 顶部控制栏
 */
@Composable
private fun TopControlBar(
    onNavigateBack: () -> Unit,
    onShare: () -> Unit,
    onDelete: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.7f)
        ),
        shape = RoundedCornerShape(24.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 返回按钮
            IconButton(
                onClick = onNavigateBack
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "返回",
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }

            Row {
                // 分享按钮
                IconButton(
                    onClick = onShare
                ) {
                    Icon(
                        imageVector = Icons.Default.Share,
                        contentDescription = "分享",
                        tint = Color.White,
                        modifier = Modifier.size(24.dp)
                    )
                }

                Spacer(modifier = Modifier.width(8.dp))

                // 删除按钮
                IconButton(
                    onClick = onDelete
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "删除",
                        tint = Color.White,
                        modifier = Modifier.size(24.dp)
                    )
                }
            }
        }
    }
}

/**
 * 底部控制栏
 */
@Composable
private fun BottomControlBar(
    onEdit: () -> Unit,
    onResetTransform: () -> Unit,
    onToggleControls: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.7f)
        ),
        shape = RoundedCornerShape(24.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 编辑按钮
            IconButton(
                onClick = {
                    onEdit()
                }
            ) {
                Icon(
                    imageVector = Icons.Default.Edit,
                    contentDescription = "编辑",
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }

            // 重置变换按钮
            IconButton(
                onClick = onResetTransform
            ) {
                Icon(
                    imageVector = Icons.Default.CenterFocusWeak,
                    contentDescription = "重置",
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }

            // 信息按钮
            IconButton(
                onClick = {
                    // 显示图片信息的功能可以后续添加
                }
            ) {
                Icon(
                    imageVector = Icons.Default.Info,
                    contentDescription = "信息",
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
}
