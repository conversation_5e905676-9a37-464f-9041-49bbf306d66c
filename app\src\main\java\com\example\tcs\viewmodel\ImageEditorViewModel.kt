package com.example.tcs.viewmodel

import android.content.Context
import android.graphics.*
import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.tcs.ui.editor.EditTool
import com.example.tcs.ui.editor.ImageFilter
import com.example.tcs.utils.MediaUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.InputStream

/**
 * 图片编辑ViewModel
 */
class ImageEditorViewModel : ViewModel() {
    
    private val _editState = MutableStateFlow(ImageEditState())
    val editState: StateFlow<ImageEditState> = _editState.asStateFlow()
    
    private var originalBitmap: Bitmap? = null
    
    /**
     * 加载图片
     */
    fun loadImage(context: Context, imageUri: Uri) {
        viewModelScope.launch {
            try {
                val bitmap = withContext(Dispatchers.IO) {
                    loadBitmapFromUri(context, imageUri)
                }
                originalBitmap = bitmap
                _editState.value = _editState.value.copy(
                    editedBitmap = bitmap,
                    isLoading = false
                )
            } catch (e: Exception) {
                _editState.value = _editState.value.copy(
                    isLoading = false,
                    error = "加载图片失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 选择编辑工具
     */
    fun selectTool(tool: EditTool) {
        _editState.value = _editState.value.copy(currentTool = tool)
    }
    
    /**
     * 调整亮度
     */
    fun adjustBrightness(brightness: Float) {
        _editState.value = _editState.value.copy(brightness = brightness)
        applyAllAdjustments()
    }
    
    /**
     * 调整对比度
     */
    fun adjustContrast(contrast: Float) {
        _editState.value = _editState.value.copy(contrast = contrast)
        applyAllAdjustments()
    }
    
    /**
     * 调整饱和度
     */
    fun adjustSaturation(saturation: Float) {
        _editState.value = _editState.value.copy(saturation = saturation)
        applyAllAdjustments()
    }
    
    /**
     * 应用滤镜
     */
    fun applyFilter(filter: ImageFilter) {
        _editState.value = _editState.value.copy(selectedFilter = filter)
        applyAllAdjustments()
    }
    
    /**
     * 旋转图片
     */
    fun rotateImage(degrees: Float) {
        viewModelScope.launch {
            val currentBitmap = _editState.value.editedBitmap ?: return@launch
            val rotatedBitmap = withContext(Dispatchers.Default) {
                rotateBitmap(currentBitmap, degrees)
            }
            _editState.value = _editState.value.copy(editedBitmap = rotatedBitmap)
        }
    }
    
    /**
     * 翻转图片
     */
    fun flipImage(horizontal: Boolean) {
        viewModelScope.launch {
            val currentBitmap = _editState.value.editedBitmap ?: return@launch
            val flippedBitmap = withContext(Dispatchers.Default) {
                flipBitmap(currentBitmap, horizontal)
            }
            _editState.value = _editState.value.copy(editedBitmap = flippedBitmap)
        }
    }
    
    /**
     * 保存编辑后的图片
     */
    fun saveEditedImage(context: Context, onResult: (Uri?) -> Unit) {
        viewModelScope.launch {
            try {
                val bitmap = _editState.value.editedBitmap ?: return@launch
                val savedUri = withContext(Dispatchers.IO) {
                    MediaUtils.saveImageToGallery(context, bitmap)
                }
                onResult(savedUri)
            } catch (e: Exception) {
                _editState.value = _editState.value.copy(
                    error = "保存图片失败: ${e.message}"
                )
                onResult(null)
            }
        }
    }
    
    /**
     * 应用所有调整
     */
    private fun applyAllAdjustments() {
        viewModelScope.launch {
            val original = originalBitmap ?: return@launch
            val state = _editState.value
            
            val adjustedBitmap = withContext(Dispatchers.Default) {
                var bitmap = original.copy(original.config ?: Bitmap.Config.ARGB_8888, true)
                
                // 应用亮度、对比度、饱和度调整
                bitmap = applyColorAdjustments(bitmap, state.brightness, state.contrast, state.saturation)
                
                // 应用滤镜
                bitmap = applyImageFilter(bitmap, state.selectedFilter)
                
                bitmap
            }
            
            _editState.value = _editState.value.copy(editedBitmap = adjustedBitmap)
        }
    }
    
    /**
     * 从Uri加载Bitmap
     */
    private suspend fun loadBitmapFromUri(context: Context, uri: Uri): Bitmap {
        return withContext(Dispatchers.IO) {
            val inputStream: InputStream? = context.contentResolver.openInputStream(uri)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            inputStream?.close()
            bitmap ?: throw IllegalArgumentException("无法解码图片")
        }
    }
    
    /**
     * 应用颜色调整
     */
    private fun applyColorAdjustments(
        bitmap: Bitmap,
        brightness: Float,
        contrast: Float,
        saturation: Float
    ): Bitmap {
        val colorMatrix = ColorMatrix()
        
        // 亮度调整
        val brightnessValue = brightness / 100f * 255f
        colorMatrix.set(floatArrayOf(
            1f, 0f, 0f, 0f, brightnessValue,
            0f, 1f, 0f, 0f, brightnessValue,
            0f, 0f, 1f, 0f, brightnessValue,
            0f, 0f, 0f, 1f, 0f
        ))
        
        // 对比度调整
        val contrastValue = (contrast + 100f) / 100f
        val contrastMatrix = ColorMatrix(floatArrayOf(
            contrastValue, 0f, 0f, 0f, 0f,
            0f, contrastValue, 0f, 0f, 0f,
            0f, 0f, contrastValue, 0f, 0f,
            0f, 0f, 0f, 1f, 0f
        ))
        colorMatrix.postConcat(contrastMatrix)
        
        // 饱和度调整
        val saturationValue = (saturation + 100f) / 100f
        val saturationMatrix = ColorMatrix()
        saturationMatrix.setSaturation(saturationValue)
        colorMatrix.postConcat(saturationMatrix)
        
        val paint = Paint()
        paint.colorFilter = ColorMatrixColorFilter(colorMatrix)
        
        val result = Bitmap.createBitmap(bitmap.width, bitmap.height, bitmap.config ?: Bitmap.Config.ARGB_8888)
        val canvas = Canvas(result)
        canvas.drawBitmap(bitmap, 0f, 0f, paint)
        
        return result
    }
    
    /**
     * 应用图片滤镜
     */
    private fun applyImageFilter(bitmap: Bitmap, filter: ImageFilter): Bitmap {
        return when (filter) {
            ImageFilter.NONE -> bitmap
            ImageFilter.GRAYSCALE -> applyGrayscaleFilter(bitmap)
            ImageFilter.SEPIA -> applySepiaFilter(bitmap)
            ImageFilter.VINTAGE -> applyVintageFilter(bitmap)
            ImageFilter.WARM -> applyWarmFilter(bitmap)
            ImageFilter.COOL -> applyCoolFilter(bitmap)
            ImageFilter.VIVID -> applyVividFilter(bitmap)
            ImageFilter.DRAMATIC -> applyDramaticFilter(bitmap)
        }
    }
    
    /**
     * 黑白滤镜
     */
    private fun applyGrayscaleFilter(bitmap: Bitmap): Bitmap {
        val colorMatrix = ColorMatrix()
        colorMatrix.setSaturation(0f)
        
        val paint = Paint()
        paint.colorFilter = ColorMatrixColorFilter(colorMatrix)
        
        val result = Bitmap.createBitmap(bitmap.width, bitmap.height, bitmap.config ?: Bitmap.Config.ARGB_8888)
        val canvas = Canvas(result)
        canvas.drawBitmap(bitmap, 0f, 0f, paint)

        return result
    }

    /**
     * 复古滤镜
     */
    private fun applySepiaFilter(bitmap: Bitmap): Bitmap {
        val colorMatrix = ColorMatrix(floatArrayOf(
            0.393f, 0.769f, 0.189f, 0f, 0f,
            0.349f, 0.686f, 0.168f, 0f, 0f,
            0.272f, 0.534f, 0.131f, 0f, 0f,
            0f, 0f, 0f, 1f, 0f
        ))

        val paint = Paint()
        paint.colorFilter = ColorMatrixColorFilter(colorMatrix)

        val result = Bitmap.createBitmap(bitmap.width, bitmap.height, bitmap.config ?: Bitmap.Config.ARGB_8888)
        val canvas = Canvas(result)
        canvas.drawBitmap(bitmap, 0f, 0f, paint)
        
        return result
    }
    
    /**
     * 怀旧滤镜
     */
    private fun applyVintageFilter(bitmap: Bitmap): Bitmap {
        val colorMatrix = ColorMatrix(floatArrayOf(
            1.2f, 0f, 0f, 0f, 20f,
            0f, 1.1f, 0f, 0f, 10f,
            0f, 0f, 0.8f, 0f, 0f,
            0f, 0f, 0f, 1f, 0f
        ))
        
        val paint = Paint()
        paint.colorFilter = ColorMatrixColorFilter(colorMatrix)
        
        val result = Bitmap.createBitmap(bitmap.width, bitmap.height, bitmap.config ?: Bitmap.Config.ARGB_8888)
        val canvas = Canvas(result)
        canvas.drawBitmap(bitmap, 0f, 0f, paint)

        return result
    }

    /**
     * 暖色滤镜
     */
    private fun applyWarmFilter(bitmap: Bitmap): Bitmap {
        val colorMatrix = ColorMatrix(floatArrayOf(
            1.2f, 0f, 0f, 0f, 15f,
            0f, 1.1f, 0f, 0f, 10f,
            0f, 0f, 0.9f, 0f, 0f,
            0f, 0f, 0f, 1f, 0f
        ))

        val paint = Paint()
        paint.colorFilter = ColorMatrixColorFilter(colorMatrix)

        val result = Bitmap.createBitmap(bitmap.width, bitmap.height, bitmap.config ?: Bitmap.Config.ARGB_8888)
        val canvas = Canvas(result)
        canvas.drawBitmap(bitmap, 0f, 0f, paint)
        
        return result
    }
    
    /**
     * 冷色滤镜
     */
    private fun applyCoolFilter(bitmap: Bitmap): Bitmap {
        val colorMatrix = ColorMatrix(floatArrayOf(
            0.9f, 0f, 0f, 0f, 0f,
            0f, 1.0f, 0f, 0f, 0f,
            0f, 0f, 1.2f, 0f, 15f,
            0f, 0f, 0f, 1f, 0f
        ))
        
        val paint = Paint()
        paint.colorFilter = ColorMatrixColorFilter(colorMatrix)
        
        val result = Bitmap.createBitmap(bitmap.width, bitmap.height, bitmap.config ?: Bitmap.Config.ARGB_8888)
        val canvas = Canvas(result)
        canvas.drawBitmap(bitmap, 0f, 0f, paint)

        return result
    }

    /**
     * 鲜艳滤镜
     */
    private fun applyVividFilter(bitmap: Bitmap): Bitmap {
        val colorMatrix = ColorMatrix()
        colorMatrix.setSaturation(1.5f)

        val paint = Paint()
        paint.colorFilter = ColorMatrixColorFilter(colorMatrix)

        val result = Bitmap.createBitmap(bitmap.width, bitmap.height, bitmap.config ?: Bitmap.Config.ARGB_8888)
        val canvas = Canvas(result)
        canvas.drawBitmap(bitmap, 0f, 0f, paint)
        
        return result
    }
    
    /**
     * 戏剧滤镜
     */
    private fun applyDramaticFilter(bitmap: Bitmap): Bitmap {
        val colorMatrix = ColorMatrix(floatArrayOf(
            1.5f, 0f, 0f, 0f, -20f,
            0f, 1.5f, 0f, 0f, -20f,
            0f, 0f, 1.5f, 0f, -20f,
            0f, 0f, 0f, 1f, 0f
        ))
        
        val paint = Paint()
        paint.colorFilter = ColorMatrixColorFilter(colorMatrix)
        
        val result = Bitmap.createBitmap(bitmap.width, bitmap.height, bitmap.config ?: Bitmap.Config.ARGB_8888)
        val canvas = Canvas(result)
        canvas.drawBitmap(bitmap, 0f, 0f, paint)

        return result
    }
    
    /**
     * 旋转Bitmap
     */
    private fun rotateBitmap(bitmap: Bitmap, degrees: Float): Bitmap {
        val matrix = Matrix()
        matrix.postRotate(degrees)
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
    }
    
    /**
     * 翻转Bitmap
     */
    private fun flipBitmap(bitmap: Bitmap, horizontal: Boolean): Bitmap {
        val matrix = Matrix()
        if (horizontal) {
            matrix.preScale(-1f, 1f)
        } else {
            matrix.preScale(1f, -1f)
        }
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
    }
}

/**
 * 图片编辑状态
 */
data class ImageEditState(
    val editedBitmap: Bitmap? = null,
    val currentTool: EditTool = EditTool.BRIGHTNESS,
    val brightness: Float = 0f,
    val contrast: Float = 0f,
    val saturation: Float = 0f,
    val selectedFilter: ImageFilter = ImageFilter.NONE,
    val isLoading: Boolean = true,
    val error: String? = null
)
