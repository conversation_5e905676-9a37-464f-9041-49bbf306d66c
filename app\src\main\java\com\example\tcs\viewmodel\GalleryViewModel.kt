package com.example.tcs.viewmodel

import android.content.Context
import android.net.Uri
import android.provider.MediaStore
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.tcs.ui.gallery.MediaFile
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

/**
 * 相册ViewModel
 */
class GalleryViewModel : ViewModel() {
    
    private val _mediaFiles = MutableStateFlow<List<MediaFile>>(emptyList())
    val mediaFiles: StateFlow<List<MediaFile>> = _mediaFiles.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    /**
     * 加载媒体文件
     */
    fun loadMediaFiles(context: Context) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val files = withContext(Dispatchers.IO) {
                    loadImagesAndVideos(context)
                }
                _mediaFiles.value = files.sortedByDescending { it.dateAdded }
            } catch (e: Exception) {
                // 处理错误
                _mediaFiles.value = emptyList()
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 从MediaStore加载图片和视频
     */
    private suspend fun loadImagesAndVideos(context: Context): List<MediaFile> {
        val mediaFiles = mutableListOf<MediaFile>()
        
        // 加载图片
        val imageProjection = arrayOf(
            MediaStore.Images.Media._ID,
            MediaStore.Images.Media.DISPLAY_NAME,
            MediaStore.Images.Media.DATE_ADDED,
            MediaStore.Images.Media.SIZE
        )
        
        val imageCursor = context.contentResolver.query(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            imageProjection,
            null,
            null,
            "${MediaStore.Images.Media.DATE_ADDED} DESC"
        )
        
        imageCursor?.use { cursor ->
            val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID)
            val nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DISPLAY_NAME)
            val dateColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_ADDED)
            val sizeColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.SIZE)
            
            while (cursor.moveToNext()) {
                val id = cursor.getLong(idColumn)
                val name = cursor.getString(nameColumn)
                val dateAdded = cursor.getLong(dateColumn)
                val size = cursor.getLong(sizeColumn)
                
                val uri = Uri.withAppendedPath(
                    MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                    id.toString()
                )
                
                mediaFiles.add(
                    MediaFile(
                        uri = uri,
                        name = name,
                        dateAdded = dateAdded,
                        size = size,
                        isVideo = false
                    )
                )
            }
        }
        
        // 加载视频
        val videoProjection = arrayOf(
            MediaStore.Video.Media._ID,
            MediaStore.Video.Media.DISPLAY_NAME,
            MediaStore.Video.Media.DATE_ADDED,
            MediaStore.Video.Media.SIZE,
            MediaStore.Video.Media.DURATION
        )
        
        val videoCursor = context.contentResolver.query(
            MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
            videoProjection,
            null,
            null,
            "${MediaStore.Video.Media.DATE_ADDED} DESC"
        )
        
        videoCursor?.use { cursor ->
            val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media._ID)
            val nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DISPLAY_NAME)
            val dateColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DATE_ADDED)
            val sizeColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.SIZE)
            val durationColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DURATION)
            
            while (cursor.moveToNext()) {
                val id = cursor.getLong(idColumn)
                val name = cursor.getString(nameColumn)
                val dateAdded = cursor.getLong(dateColumn)
                val size = cursor.getLong(sizeColumn)
                val duration = cursor.getLong(durationColumn)
                
                val uri = Uri.withAppendedPath(
                    MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
                    id.toString()
                )
                
                mediaFiles.add(
                    MediaFile(
                        uri = uri,
                        name = name,
                        dateAdded = dateAdded,
                        size = size,
                        isVideo = true,
                        duration = duration
                    )
                )
            }
        }
        
        // 同时加载应用内部存储的文件
        val appFiles = loadAppInternalFiles(context)
        mediaFiles.addAll(appFiles)
        
        return mediaFiles
    }
    
    /**
     * 加载应用内部存储的文件
     */
    private fun loadAppInternalFiles(context: Context): List<MediaFile> {
        val mediaFiles = mutableListOf<MediaFile>()
        
        try {
            val externalFilesDir = context.getExternalFilesDir(null)
            externalFilesDir?.listFiles()?.forEach { file ->
                if (file.isFile && (file.extension.lowercase() in listOf("jpg", "jpeg", "png", "mp4", "mov"))) {
                    val isVideo = file.extension.lowercase() in listOf("mp4", "mov")
                    mediaFiles.add(
                        MediaFile(
                            uri = Uri.fromFile(file),
                            name = file.name,
                            dateAdded = file.lastModified() / 1000,
                            size = file.length(),
                            isVideo = isVideo
                        )
                    )
                }
            }
        } catch (e: Exception) {
            // 忽略错误
        }
        
        return mediaFiles
    }
    
    /**
     * 删除媒体文件
     */
    fun deleteMediaFile(context: Context, mediaFile: MediaFile) {
        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    if (mediaFile.uri.scheme == "file") {
                        // 删除应用内部文件
                        val file = File(mediaFile.uri.path ?: return@withContext)
                        file.delete()
                    } else {
                        // 删除MediaStore中的文件
                        context.contentResolver.delete(mediaFile.uri, null, null)
                    }
                }
                // 重新加载文件列表
                loadMediaFiles(context)
            } catch (e: Exception) {
                // 处理删除错误
            }
        }
    }
}
