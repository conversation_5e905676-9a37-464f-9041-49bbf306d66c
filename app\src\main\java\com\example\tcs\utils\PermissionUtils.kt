package com.example.tcs.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.content.ContextCompat

/**
 * 权限工具类
 */
object PermissionUtils {
    
    /**
     * 获取相机所需的权限列表
     */
    fun getCameraPermissions(): Array<String> {
        val permissions = mutableListOf<String>()
        
        // 相机权限
        permissions.add(Manifest.permission.CAMERA)
        
        // 录音权限（录像需要）
        permissions.add(Manifest.permission.RECORD_AUDIO)
        
        // 存储权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permissions.add(Manifest.permission.READ_MEDIA_IMAGES)
            permissions.add(Manifest.permission.READ_MEDIA_VIDEO)
        } else {
            permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
            if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.P) {
                permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            }
        }
        
        return permissions.toTypedArray()
    }
    
    /**
     * 检查是否已授予所有必要权限
     */
    fun hasAllPermissions(context: Context): Boolean {
        return getCameraPermissions().all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 检查相机权限
     */
    fun hasCameraPermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查录音权限
     */
    fun hasAudioPermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查存储权限
     */
    fun hasStoragePermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_MEDIA_IMAGES
            ) == PackageManager.PERMISSION_GRANTED &&
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_MEDIA_VIDEO
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 获取权限说明文本
     */
    fun getPermissionRationale(permission: String): String {
        return when (permission) {
            Manifest.permission.CAMERA -> "需要相机权限来拍照和录像"
            Manifest.permission.RECORD_AUDIO -> "需要录音权限来录制视频音频"
            Manifest.permission.READ_EXTERNAL_STORAGE -> "需要存储权限来保存和读取照片视频"
            Manifest.permission.WRITE_EXTERNAL_STORAGE -> "需要存储权限来保存照片视频"
            Manifest.permission.READ_MEDIA_IMAGES -> "需要权限来访问照片"
            Manifest.permission.READ_MEDIA_VIDEO -> "需要权限来访问视频"
            else -> "需要此权限来正常使用相机功能"
        }
    }
}
