package com.example.tcs.ui.camera

import android.net.Uri
import androidx.camera.view.PreviewView
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.tcs.camera.CameraState
import com.example.tcs.data.CaptureMode
import com.example.tcs.data.FlashMode
import com.example.tcs.viewmodel.CameraViewModel
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberMultiplePermissionsState
import com.example.tcs.utils.PermissionUtils

/**
 * 相机预览界面
 */
@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun CameraPreviewScreen(
    onNavigateToSettings: () -> Unit,
    onNavigateToGallery: () -> Unit,
    viewModel: CameraViewModel = viewModel()
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    
    // 权限状态
    val permissionsState = rememberMultiplePermissionsState(
        permissions = PermissionUtils.getCameraPermissions().toList()
    )
    
    // 相机状态
    val cameraState by viewModel.cameraState.collectAsState()
    val settings by viewModel.settings.collectAsState()
    val error by viewModel.error.collectAsState()
    
    // UI状态
    var showFlashOptions by remember { mutableStateOf(false) }
    var lastCapturedImage by remember { mutableStateOf<Uri?>(null) }
    
    // 检查权限
    LaunchedEffect(Unit) {
        if (!permissionsState.allPermissionsGranted) {
            permissionsState.launchMultiplePermissionRequest()
        }
    }
    

    
    Box(modifier = Modifier.fillMaxSize()) {
        // 相机预览
        var previewView by remember { mutableStateOf<PreviewView?>(null) }

        if (permissionsState.allPermissionsGranted) {
            AndroidView(
                factory = { context ->
                    PreviewView(context).apply {
                        scaleType = PreviewView.ScaleType.FILL_CENTER
                        previewView = this
                    }
                },
                modifier = Modifier.fillMaxSize()
            )

            // 初始化相机
            LaunchedEffect(previewView) {
                previewView?.let {
                    viewModel.initializeCamera(lifecycleOwner, it)
                }
            }
        } else {
            // 显示权限请求界面
            PermissionRequestScreen(
                permissionsState = permissionsState,
                onRequestPermissions = { permissionsState.launchMultiplePermissionRequest() }
            )
        }
        
        // 顶部控制栏
        TopControlBar(
            settings = settings,
            onFlashClick = { showFlashOptions = !showFlashOptions },
            onSettingsClick = onNavigateToSettings,
            modifier = Modifier.align(Alignment.TopCenter)
        )
        
        // 闪光灯选项
        if (showFlashOptions) {
            FlashOptionsMenu(
                currentFlashMode = settings.flashMode,
                onFlashModeSelected = { mode ->
                    viewModel.updateFlashMode(mode)
                    showFlashOptions = false
                },
                onDismiss = { showFlashOptions = false },
                modifier = Modifier.align(Alignment.TopEnd)
            )
        }
        
        // 底部控制栏
        BottomControlBar(
            cameraState = cameraState,
            captureMode = settings.captureMode,
            onCaptureClick = {
                when (settings.captureMode) {
                    CaptureMode.PHOTO -> {
                        viewModel.takePhoto { success, uri, errorMsg ->
                            if (success && uri != null) {
                                lastCapturedImage = uri
                            }
                        }
                    }
                    CaptureMode.VIDEO -> {
                        if (cameraState == CameraState.RECORDING) {
                            viewModel.stopVideoRecording()
                        } else {
                            viewModel.startVideoRecording { _, _ -> }
                        }
                    }
                    else -> {
                        // 其他模式的处理
                    }
                }
            },
            onSwitchCamera = {
                previewView?.let { viewModel.switchCamera(lifecycleOwner, it) }
            },
            onGalleryClick = onNavigateToGallery,
            onCaptureModeChange = { mode -> viewModel.updateCaptureMode(mode) },
            modifier = Modifier.align(Alignment.BottomCenter)
        )
        
        // 错误提示
        error?.let { errorMessage ->
            Card(
                modifier = Modifier
                    .align(Alignment.Center)
                    .padding(16.dp),
                colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.errorContainer)
            ) {
                Text(
                    text = errorMessage,
                    modifier = Modifier.padding(16.dp),
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
            }
        }
        
        // 加载指示器
        if (cameraState == CameraState.INITIALIZING) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.5f)),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(color = MaterialTheme.colorScheme.primary)
            }
        }
    }
}

/**
 * 顶部控制栏
 */
@Composable
private fun TopControlBar(
    settings: com.example.tcs.data.CameraSettings,
    onFlashClick: () -> Unit,
    onSettingsClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 闪光灯按钮
        IconButton(
            onClick = onFlashClick,
            modifier = Modifier
                .background(
                    Color.Black.copy(alpha = 0.3f),
                    CircleShape
                )
        ) {
            Icon(
                imageVector = when (settings.flashMode) {
                    FlashMode.AUTO -> Icons.Default.FlashAuto
                    FlashMode.ON -> Icons.Default.FlashOn
                    FlashMode.OFF -> Icons.Default.FlashOff
                    else -> Icons.Default.FlashAuto
                },
                contentDescription = "闪光灯",
                tint = Color.White
            )
        }
        
        Spacer(modifier = Modifier.weight(1f))
        
        // 设置按钮
        IconButton(
            onClick = onSettingsClick,
            modifier = Modifier
                .background(
                    Color.Black.copy(alpha = 0.3f),
                    CircleShape
                )
        ) {
            Icon(
                imageVector = Icons.Default.Settings,
                contentDescription = "设置",
                tint = Color.White
            )
        }
    }
}

/**
 * 闪光灯选项菜单
 */
@Composable
private fun FlashOptionsMenu(
    currentFlashMode: Int,
    onFlashModeSelected: (Int) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.padding(16.dp),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(modifier = Modifier.padding(8.dp)) {
            FlashOption(
                icon = Icons.Default.FlashAuto,
                text = "自动",
                isSelected = currentFlashMode == FlashMode.AUTO,
                onClick = { onFlashModeSelected(FlashMode.AUTO) }
            )
            FlashOption(
                icon = Icons.Default.FlashOn,
                text = "开启",
                isSelected = currentFlashMode == FlashMode.ON,
                onClick = { onFlashModeSelected(FlashMode.ON) }
            )
            FlashOption(
                icon = Icons.Default.FlashOff,
                text = "关闭",
                isSelected = currentFlashMode == FlashMode.OFF,
                onClick = { onFlashModeSelected(FlashMode.OFF) }
            )
        }
    }
}

/**
 * 闪光灯选项
 */
@Composable
private fun FlashOption(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .clickable(onClick = onClick),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = text,
            tint = if (isSelected) MaterialTheme.colorScheme.primary else LocalContentColor.current
        )
        Spacer(modifier = Modifier.width(16.dp))
        Text(
            text = text,
            style = MaterialTheme.typography.bodyLarge,
            color = if (isSelected) MaterialTheme.colorScheme.primary else LocalContentColor.current
        )
    }
}

/**
 * 底部控制栏
 */
@Composable
private fun BottomControlBar(
    cameraState: CameraState,
    captureMode: CaptureMode,
    onCaptureClick: () -> Unit,
    onSwitchCamera: () -> Unit,
    onGalleryClick: () -> Unit,
    onCaptureModeChange: (CaptureMode) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(bottom = 32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 拍摄模式选择器
        CaptureModeSelector(
            currentMode = captureMode,
            onModeSelected = onCaptureModeChange
        )

        Spacer(modifier = Modifier.height(24.dp))

        // 底部按钮行
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 相册按钮
            IconButton(
                onClick = onGalleryClick,
                modifier = Modifier
                    .size(48.dp)
                    .background(Color.Black.copy(alpha = 0.3f), CircleShape)
            ) {
                Icon(
                    imageVector = Icons.Default.PhotoLibrary,
                    contentDescription = "相册",
                    tint = Color.White
                )
            }

            // 拍摄按钮
            CaptureButton(
                cameraState = cameraState,
                captureMode = captureMode,
                onClick = onCaptureClick
            )

            // 切换相机按钮
            IconButton(
                onClick = onSwitchCamera,
                modifier = Modifier
                    .size(48.dp)
                    .background(Color.Black.copy(alpha = 0.3f), CircleShape)
            ) {
                Icon(
                    imageVector = Icons.Default.FlipCameraAndroid,
                    contentDescription = "切换相机",
                    tint = Color.White
                )
            }
        }
    }
}

/**
 * 拍摄模式选择器
 */
@Composable
private fun CaptureModeSelector(
    currentMode: CaptureMode,
    onModeSelected: (CaptureMode) -> Unit
) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        val modes = listOf(
            CaptureMode.PHOTO to "照片",
            CaptureMode.VIDEO to "视频",
            CaptureMode.PORTRAIT to "人像",
            CaptureMode.NIGHT to "夜景"
        )

        modes.forEach { (mode, text) ->
            Text(
                text = text,
                style = MaterialTheme.typography.bodyMedium,
                color = if (currentMode == mode) Color.White else Color.White.copy(alpha = 0.6f),
                modifier = Modifier
                    .clickable { onModeSelected(mode) }
                    .padding(horizontal = 8.dp, vertical = 4.dp)
            )
        }
    }
}

/**
 * 拍摄按钮
 */
@Composable
private fun CaptureButton(
    cameraState: CameraState,
    captureMode: CaptureMode,
    onClick: () -> Unit
) {
    val isRecording = cameraState == CameraState.RECORDING
    val isCapturing = cameraState == CameraState.CAPTURING

    Box(
        modifier = Modifier
            .size(80.dp)
            .background(
                if (isRecording) Color.Red else Color.White,
                CircleShape
            )
            .clickable(enabled = cameraState == CameraState.READY || isRecording) {
                onClick()
            },
        contentAlignment = Alignment.Center
    ) {
        when {
            isCapturing -> {
                CircularProgressIndicator(
                    modifier = Modifier.size(40.dp),
                    color = MaterialTheme.colorScheme.primary
                )
            }
            captureMode == CaptureMode.VIDEO && isRecording -> {
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .background(Color.White, RoundedCornerShape(4.dp))
                )
            }
            else -> {
                Box(
                    modifier = Modifier
                        .size(60.dp)
                        .background(
                            if (captureMode == CaptureMode.VIDEO) Color.Red else Color.Black,
                            CircleShape
                        )
                )
            }
        }
    }
}

/**
 * 权限请求界面
 */
@OptIn(ExperimentalPermissionsApi::class)
@Composable
private fun PermissionRequestScreen(
    permissionsState: com.google.accompanist.permissions.MultiplePermissionsState,
    onRequestPermissions: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.CameraAlt,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.primary
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "需要权限",
            style = MaterialTheme.typography.headlineMedium
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = "此应用需要相机和存储权限才能正常工作",
            style = MaterialTheme.typography.bodyLarge,
            textAlign = androidx.compose.ui.text.style.TextAlign.Center
        )

        Spacer(modifier = Modifier.height(24.dp))

        Button(
            onClick = onRequestPermissions,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("授予权限")
        }
    }
}
